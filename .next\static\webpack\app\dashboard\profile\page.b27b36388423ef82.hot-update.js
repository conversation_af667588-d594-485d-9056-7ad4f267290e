"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/lib/services/kyc.ts":
/*!*********************************!*\
  !*** ./src/lib/services/kyc.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCService: function() { return /* binding */ KYCService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_supabaseConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabaseConfig */ \"(app-pages-browser)/./src/lib/supabaseConfig.ts\");\n/* harmony import */ var _kycStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./kycStorage */ \"(app-pages-browser)/./src/lib/services/kycStorage.ts\");\n\n\n\nclass KYCService {\n    /**\n   * Get user's KYC status from users table\n   */ static async getUserKYCStatus(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabaseConfig__WEBPACK_IMPORTED_MODULE_1__.TABLES.USERS).select(\"kyc_status, kyc_submitted_at, kyc_approved_at\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user KYC status:\", error);\n                // Return default status if columns don't exist yet\n                if (error.message.includes(\"column\") && error.message.includes(\"does not exist\")) {\n                    return {\n                        kyc_status: \"not_submitted\"\n                    };\n                }\n                throw new Error(\"Failed to fetch KYC status: \".concat(error.message));\n            }\n            return data || {\n                kyc_status: \"not_submitted\"\n            };\n        } catch (error) {\n            console.error(\"Error getting user KYC status:\", error);\n            // Return default status for any database errors\n            return {\n                kyc_status: \"not_submitted\"\n            };\n        }\n    }\n    /**\n   * Get user's KYC submission details\n   */ static async getUserKYCSubmission(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabaseConfig__WEBPACK_IMPORTED_MODULE_1__.TABLES.KYC_SUBMISSIONS).select(\"*\").eq(\"user_id\", userId).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    // No submission found\n                    return null;\n                }\n                // Handle table not found error\n                if (error.message.includes(\"Could not find the table\") || error.message.includes(\"schema cache\")) {\n                    console.warn(\"KYC submissions table not found - database migration may be needed\");\n                    return null;\n                }\n                console.error(\"Error fetching KYC submission:\", error);\n                throw new Error(\"Failed to fetch KYC submission: \".concat(error.message));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting user KYC submission:\", error);\n            // Return null for table not found errors instead of throwing\n            if (error instanceof Error && (error.message.includes(\"Could not find the table\") || error.message.includes(\"schema cache\"))) {\n                return null;\n            }\n            throw error;\n        }\n    }\n    /**\n   * Submit KYC application with documents\n   */ static async submitKYCApplication(userId, submissionData, documents) {\n        try {\n            // Check if user already has a submission\n            const existingSubmission = await this.getUserKYCSubmission(userId);\n            if (existingSubmission && existingSubmission.status !== \"rejected\") {\n                throw new Error(\"You already have a pending or approved KYC submission\");\n            }\n            // Prepare document uploads\n            const documentUploads = [\n                {\n                    file: documents.id_front,\n                    type: \"id_front\"\n                },\n                {\n                    file: documents.id_back,\n                    type: \"id_back\"\n                },\n                {\n                    file: documents.selfie,\n                    type: \"selfie\"\n                },\n                {\n                    file: documents.address_proof,\n                    type: \"address_proof\"\n                }\n            ];\n            // Upload all documents\n            console.log(\"Uploading KYC documents...\");\n            const uploadedDocuments = await _kycStorage__WEBPACK_IMPORTED_MODULE_2__.KYCStorageService.uploadKYCDocuments(documentUploads, userId);\n            // If there's an existing rejected submission, delete it first\n            if (existingSubmission) {\n                await this.deleteKYCSubmission(existingSubmission.id);\n            }\n            // Create KYC submission record\n            const submissionRecord = {\n                user_id: userId,\n                id_document_front_url: uploadedDocuments.id_front,\n                id_document_back_url: uploadedDocuments.id_back,\n                selfie_photo_url: uploadedDocuments.selfie,\n                address_proof_url: uploadedDocuments.address_proof,\n                id_document_type: submissionData.id_document_type,\n                id_document_number: submissionData.id_document_number,\n                full_name: submissionData.full_name,\n                date_of_birth: submissionData.date_of_birth,\n                address: submissionData.address,\n                submission_notes: submissionData.submission_notes,\n                status: \"pending\"\n            };\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabaseConfig__WEBPACK_IMPORTED_MODULE_1__.TABLES.KYC_SUBMISSIONS).insert(submissionRecord).select().single();\n            if (error) {\n                console.error(\"Error creating KYC submission:\", error);\n                throw new Error(\"Failed to submit KYC application: \".concat(error.message));\n            }\n            console.log(\"KYC application submitted successfully\");\n            return data;\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all KYC submissions for admin review\n   */ static async getAllKYCSubmissions() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, status = arguments.length > 2 ? arguments[2] : void 0, searchTerm = arguments.length > 3 ? arguments[3] : void 0;\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabaseConfig__WEBPACK_IMPORTED_MODULE_1__.TABLES.KYC_SUBMISSIONS).select(\"\\n          *,\\n          user:users!kyc_submissions_user_id_fkey(full_name, email)\\n        \", {\n                count: \"exact\"\n            });\n            // Apply filters\n            if (status) {\n                query = query.eq(\"status\", status);\n            }\n            if (searchTerm) {\n                query = query.or(\"full_name.ilike.%\".concat(searchTerm, \"%,id_document_number.ilike.%\").concat(searchTerm, \"%\"));\n            }\n            // Apply pagination\n            const offset = (page - 1) * limit;\n            query = query.order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            const { data, error, count } = await query;\n            if (error) {\n                console.error(\"Error fetching KYC submissions:\", error);\n                throw new Error(\"Failed to fetch KYC submissions: \".concat(error.message));\n            }\n            return {\n                submissions: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting all KYC submissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Review KYC submission (admin only)\n   */ static async reviewKYCSubmission(submissionId, reviewData, reviewerId) {\n        try {\n            const updateData = {\n                status: reviewData.status,\n                reviewed_by: reviewerId,\n                reviewed_at: new Date().toISOString(),\n                rejection_reason: reviewData.rejection_reason,\n                admin_notes: reviewData.admin_notes\n            };\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabaseConfig__WEBPACK_IMPORTED_MODULE_1__.TABLES.KYC_SUBMISSIONS).update(updateData).eq(\"id\", submissionId).select().single();\n            if (error) {\n                console.error(\"Error reviewing KYC submission:\", error);\n                throw new Error(\"Failed to review KYC submission: \".concat(error.message));\n            }\n            console.log(\"KYC submission \".concat(submissionId, \" reviewed as \").concat(reviewData.status));\n            return data;\n        } catch (error) {\n            console.error(\"Error reviewing KYC submission:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get KYC status history for a user\n   */ static async getKYCStatusHistory(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabaseConfig__WEBPACK_IMPORTED_MODULE_1__.TABLES.KYC_STATUS_HISTORY).select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                console.error(\"Error fetching KYC status history:\", error);\n                throw new Error(\"Failed to fetch KYC status history: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC status history:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Check if user is KYC verified\n   */ static async isUserKYCVerified(userId) {\n        try {\n            const status = await this.getUserKYCStatus(userId);\n            return (status === null || status === void 0 ? void 0 : status.kyc_status) === \"approved\";\n        } catch (error) {\n            console.error(\"Error checking KYC verification status:\", error);\n            return false;\n        }\n    }\n    /**\n   * Get KYC document signed URLs for admin review\n   */ static async getKYCDocumentUrls(submission) {\n        try {\n            const filePaths = [\n                submission.id_document_front_url,\n                submission.id_document_back_url,\n                submission.selfie_photo_url,\n                submission.address_proof_url\n            ];\n            const signedUrls = await _kycStorage__WEBPACK_IMPORTED_MODULE_2__.KYCStorageService.getKYCDocumentSignedUrls(filePaths, 3600) // 1 hour expiry\n            ;\n            return {\n                id_front_url: signedUrls[submission.id_document_front_url],\n                id_back_url: signedUrls[submission.id_document_back_url],\n                selfie_url: signedUrls[submission.selfie_photo_url],\n                address_proof_url: signedUrls[submission.address_proof_url]\n            };\n        } catch (error) {\n            console.error(\"Error getting KYC document URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC submission (for resubmission)\n   */ static async deleteKYCSubmission(submissionId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabaseConfig__WEBPACK_IMPORTED_MODULE_1__.TABLES.KYC_SUBMISSIONS).delete().eq(\"id\", submissionId);\n            if (error) {\n                console.error(\"Error deleting KYC submission:\", error);\n                throw new Error(\"Failed to delete KYC submission: \".concat(error.message));\n            }\n        } catch (error) {\n            console.error(\"Error deleting KYC submission:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get KYC document types\n   */ static async getKYCDocumentTypes() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabaseConfig__WEBPACK_IMPORTED_MODULE_1__.TABLES.KYC_DOCUMENT_TYPES).select(\"*\").eq(\"is_active\", true).order(\"name\");\n            if (error) {\n                console.error(\"Error fetching KYC document types:\", error);\n                throw new Error(\"Failed to fetch document types: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC document types:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kyc.ts\n"));

/***/ })

});