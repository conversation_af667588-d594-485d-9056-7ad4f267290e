"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/e-store/page",{

/***/ "(app-pages-browser)/./src/lib/services/vendorShops.ts":
/*!*****************************************!*\
  !*** ./src/lib/services/vendorShops.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VendorShopService: function() { return /* binding */ VendorShopService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\n/**\n * VendorShopService - Completely rebuilt with proper error handling and relationships\n *\n * Key improvements:\n * - Uses correct foreign key constraint names from migration files\n * - Comprehensive error handling and logging\n * - Proper validation for all operations\n * - Optimized queries with proper relationships\n */ class VendorShopService {\n    /**\n   * Create a new vendor shop with comprehensive validation\n   */ static async createShop(shopData) {\n        try {\n            var _shopData_name, _shopData_description;\n            if (!shopData.user_id) {\n                throw new Error(\"User ID is required\");\n            }\n            if (!((_shopData_name = shopData.name) === null || _shopData_name === void 0 ? void 0 : _shopData_name.trim())) {\n                throw new Error(\"Shop name is required\");\n            }\n            if (!((_shopData_description = shopData.description) === null || _shopData_description === void 0 ? void 0 : _shopData_description.trim())) {\n                throw new Error(\"Shop description is required\");\n            }\n            // Check if user already has a shop\n            const existingUserShop = await this.getUserShop(shopData.user_id);\n            if (existingUserShop) {\n                throw new Error(\"You can only create one shop per account. You already have a shop.\");\n            }\n            console.log(\"Creating shop for user: \".concat(shopData.user_id, \", name: \").concat(shopData.name));\n            // Generate slug from name\n            const slug = shopData.name.toLowerCase().replace(/[^a-z0-9]+/g, \"-\").replace(/(^-|-$)/g, \"\");\n            // Check if slug already exists\n            const { data: existingSlugShop } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"id\").eq(\"slug\", \"\".concat(slug, \"-\").concat(Date.now())).single();\n            const finalSlug = existingSlugShop ? \"\".concat(slug, \"-\").concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 5)) : \"\".concat(slug, \"-\").concat(Date.now());\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).insert({\n                ...shopData,\n                name: shopData.name.trim(),\n                description: shopData.description.trim(),\n                slug: finalSlug,\n                status: \"pending\",\n                is_featured: false,\n                rating: 0,\n                total_reviews: 0,\n                total_products: 0,\n                total_sales: 0\n            }).select(\"\\n          *,\\n          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)\\n        \").single();\n            if (error) {\n                console.error(\"Error creating shop:\", error);\n                throw new Error(\"Failed to create shop: \".concat(error.message));\n            }\n            if (!data) {\n                throw new Error(\"Shop was not created\");\n            }\n            console.log(\"Shop created successfully:\", data.id);\n            return data;\n        } catch (error) {\n            console.error(\"VendorShopService.createShop error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get shop by slug with comprehensive error handling\n   */ static async getShopBySlug(slug) {\n        try {\n            if (!(slug === null || slug === void 0 ? void 0 : slug.trim())) {\n                throw new Error(\"Shop slug is required\");\n            }\n            console.log(\"Fetching shop by slug: \".concat(slug));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"\\n          *,\\n          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)\\n        \").eq(\"slug\", slug.trim()).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    console.log(\"Shop not found with slug: \".concat(slug));\n                    return null // Not found\n                    ;\n                }\n                console.error(\"Error fetching shop by slug:\", error);\n                throw new Error(\"Failed to fetch shop: \".concat(error.message));\n            }\n            if (data) {\n                console.log(\"Found shop: \".concat(data.id, \" with slug: \").concat(slug));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"VendorShopService.getShopBySlug error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's shop with comprehensive error handling\n   */ static async getUserShop(userId) {\n        try {\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            console.log(\"Fetching shop for user: \".concat(userId));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"\\n          *,\\n          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)\\n        \").eq(\"user_id\", userId).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error fetching user shop:\", error);\n                throw new Error(\"Failed to fetch user shop: \".concat(error.message));\n            }\n            if (data) {\n                console.log(\"Found shop for user \".concat(userId, \": \").concat(data.id));\n            } else {\n                console.log(\"No shop found for user: \".concat(userId));\n            }\n            return data || null;\n        } catch (error) {\n            console.error(\"VendorShopService.getUserShop error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all approved shops with pagination and comprehensive error handling\n   */ static async getApprovedShops() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            var _dataResult_data;\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching approved shops - page: \".concat(page, \", limit: \").concat(limit));\n            const [dataResult, countResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"\\n            *,\\n            user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)\\n          \").eq(\"status\", \"approved\").order(\"is_featured\", {\n                    ascending: false\n                }).order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"status\", \"approved\")\n            ]);\n            if (dataResult.error) {\n                console.error(\"Error fetching approved shops:\", dataResult.error);\n                throw new Error(\"Failed to fetch shops: \".concat(dataResult.error.message));\n            }\n            if (countResult.error) {\n                console.error(\"Error fetching approved shops count:\", countResult.error);\n                throw new Error(\"Failed to fetch shops count: \".concat(countResult.error.message));\n            }\n            console.log(\"Successfully fetched \".concat(((_dataResult_data = dataResult.data) === null || _dataResult_data === void 0 ? void 0 : _dataResult_data.length) || 0, \" approved shops (total: \").concat(countResult.count || 0, \")\"));\n            return {\n                shops: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"VendorShopService.getApprovedShops error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get shops with filters and comprehensive error handling\n   */ static async getShops() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n        try {\n            var _filters_search, _filters_location, _dataResult_data;\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching shops with filters:\", filters, \"page: \".concat(page, \", limit: \").concat(limit));\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"\\n          *,\\n          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)\\n        \").order(\"is_featured\", {\n                ascending: false\n            }).order(\"created_at\", {\n                ascending: false\n            });\n            let countQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n                countQuery = countQuery.eq(\"status\", filters.status);\n            }\n            if (filters.featured !== undefined) {\n                query = query.eq(\"is_featured\", filters.featured);\n                countQuery = countQuery.eq(\"is_featured\", filters.featured);\n            }\n            if ((_filters_search = filters.search) === null || _filters_search === void 0 ? void 0 : _filters_search.trim()) {\n                const searchTerm = \"%\".concat(filters.search.trim(), \"%\");\n                query = query.or(\"name.ilike.\".concat(searchTerm, \",description.ilike.\").concat(searchTerm));\n                countQuery = countQuery.or(\"name.ilike.\".concat(searchTerm, \",description.ilike.\").concat(searchTerm));\n            }\n            if ((_filters_location = filters.location) === null || _filters_location === void 0 ? void 0 : _filters_location.trim()) {\n                query = query.ilike(\"address\", \"%\".concat(filters.location.trim(), \"%\"));\n                countQuery = countQuery.ilike(\"address\", \"%\".concat(filters.location.trim(), \"%\"));\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.range(offset, offset + limit - 1),\n                countQuery\n            ]);\n            if (dataResult.error) {\n                console.error(\"Error fetching shops:\", dataResult.error);\n                throw new Error(\"Failed to fetch shops: \".concat(dataResult.error.message));\n            }\n            if (countResult.error) {\n                console.error(\"Error fetching shops count:\", countResult.error);\n                throw new Error(\"Failed to fetch shops count: \".concat(countResult.error.message));\n            }\n            console.log(\"Successfully fetched \".concat(((_dataResult_data = dataResult.data) === null || _dataResult_data === void 0 ? void 0 : _dataResult_data.length) || 0, \" shops (total: \").concat(countResult.count || 0, \")\"));\n            return {\n                shops: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"VendorShopService.getShops error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's shops with comprehensive error handling\n   */ static async getUserShops(userId) {\n        try {\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            console.log(\"Fetching shops for user: \".concat(userId));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"\\n          *,\\n          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)\\n        \").eq(\"user_id\", userId).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                console.error(\"Error fetching user shops:\", error);\n                throw new Error(\"Failed to fetch user shops: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" shops for user: \").concat(userId));\n            return data || [];\n        } catch (error) {\n            console.error(\"VendorShopService.getUserShops error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Update shop\n   */ static async updateShop(shopId, updates) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).update(updates).eq(\"id\", shopId).select().single();\n        if (error) {\n            throw new Error(\"Failed to update shop: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Refresh all shop statistics for all shops\n   */ static async refreshAllShopStatistics() {\n        try {\n            console.log(\"Refreshing statistics for all shops...\");\n            // Get all approved shops\n            const { data: shops, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"id, name\").eq(\"status\", \"approved\");\n            if (error) {\n                throw new Error(\"Failed to fetch shops: \".concat(error.message));\n            }\n            if (!shops || shops.length === 0) {\n                console.log(\"No approved shops found\");\n                return;\n            }\n            // Refresh statistics for each shop\n            for (const shop of shops){\n                try {\n                    await this.refreshShopStatistics(shop.id);\n                    console.log(\"✓ Updated statistics for shop: \".concat(shop.name));\n                } catch (error) {\n                    console.error(\"✗ Failed to update statistics for shop \".concat(shop.name, \":\"), error);\n                }\n            }\n            console.log(\"Completed refreshing statistics for all shops\");\n        } catch (error) {\n            console.error(\"Error refreshing all shop statistics:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Manually refresh shop statistics (products, reviews, rating, sales)\n   */ static async refreshShopStatistics(shopId) {\n        try {\n            console.log(\"Refreshing statistics for shop: \".concat(shopId));\n            // First get the product IDs for this shop\n            const { data: shopProducts, error: productsError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(\"id\").eq(\"shop_id\", shopId);\n            if (productsError) {\n                console.error(\"Error fetching shop products:\", productsError);\n                throw new Error(\"Failed to fetch shop products: \".concat(productsError.message));\n            }\n            const productIds = (shopProducts === null || shopProducts === void 0 ? void 0 : shopProducts.map((p)=>p.id)) || [];\n            // Get current statistics from related tables with individual error handling\n            const [productsResult, reviewsResult, ordersResult, viewsResult] = await Promise.allSettled([\n                // Count products\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"shop_id\", shopId),\n                // Get product reviews for rating calculation\n                productIds.length > 0 ? _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.PRODUCT_REVIEWS).select(\"rating\").in(\"product_id\", productIds) : Promise.resolve({\n                    data: [],\n                    error: null\n                }),\n                // Count completed sales\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_ORDERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"shop_id\", shopId).in(\"status\", [\n                    \"delivered\",\n                    \"completed\"\n                ]),\n                // Calculate total views from all products\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(\"views\").eq(\"shop_id\", shopId)\n            ]);\n            // Process results with error handling\n            const totalProducts = productsResult.status === \"fulfilled\" && !productsResult.value.error ? productsResult.value.count || 0 : 0;\n            const reviews = reviewsResult.status === \"fulfilled\" && !reviewsResult.value.error ? reviewsResult.value.data || [] : [];\n            const totalReviews = reviews.length;\n            const averageRating = totalReviews > 0 ? reviews.reduce((sum, review)=>sum + review.rating, 0) / totalReviews : 0;\n            const totalSales = ordersResult.status === \"fulfilled\" && !ordersResult.value.error ? ordersResult.value.count || 0 : 0;\n            const productViews = viewsResult.status === \"fulfilled\" && !viewsResult.value.error ? viewsResult.value.data || [] : [];\n            const totalViews = productViews.reduce((sum, product)=>sum + (product.views || 0), 0);\n            // Log any errors from the queries\n            if (productsResult.status === \"rejected\") {\n                console.warn(\"Failed to get products count:\", productsResult.reason);\n            }\n            if (reviewsResult.status === \"rejected\") {\n                console.warn(\"Failed to get reviews:\", reviewsResult.reason);\n            }\n            if (ordersResult.status === \"rejected\") {\n                console.warn(\"Failed to get orders count:\", ordersResult.reason);\n            }\n            if (viewsResult.status === \"rejected\") {\n                console.warn(\"Failed to get product views:\", viewsResult.reason);\n            }\n            console.log(\"Shop \".concat(shopId, \" statistics:\"), {\n                totalProducts,\n                totalReviews,\n                averageRating,\n                totalSales,\n                totalViews\n            });\n            // Update the shop with calculated statistics\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).update({\n                total_products: totalProducts,\n                total_reviews: totalReviews,\n                rating: Number(averageRating.toFixed(2)),\n                total_sales: totalSales,\n                total_views: totalViews,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", shopId).select();\n            if (error) {\n                throw new Error(\"Failed to update shop statistics: \".concat(error.message));\n            }\n            if (!data || data.length === 0) {\n                throw new Error(\"Shop not found: \".concat(shopId));\n            }\n            console.log(\"Successfully updated statistics for shop: \".concat(shopId));\n            return data[0];\n        } catch (error) {\n            console.error(\"Error refreshing shop statistics:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Add product to shop\n   */ static async addProductToShop(productData) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).insert(productData).select().single();\n        if (error) {\n            throw new Error(\"Failed to add product to shop: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get shop products - delegated to ShopProductService\n   */ static async getShopProducts(shopId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n        // Import ShopProductService dynamically to avoid circular imports\n        const { ShopProductService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_services_shopProducts_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./shopProducts */ \"(app-pages-browser)/./src/lib/services/shopProducts.ts\"));\n        return ShopProductService.getShopProducts(shopId, page, limit);\n    }\n    /**\n   * Add review to shop\n   */ static async addShopReview(reviewData) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_REVIEWS).insert(reviewData).select().single();\n        if (error) {\n            throw new Error(\"Failed to add review: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get shop reviews\n   */ static async getShopReviews(shopId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;\n        const offset = (page - 1) * limit;\n        const [dataResult, countResult] = await Promise.all([\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_REVIEWS).select(\"\\n          *,\\n          user:\".concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS, \"!shop_reviews_user_id_fkey(id, full_name, avatar_url)\\n        \")).eq(\"shop_id\", shopId).order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_REVIEWS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"shop_id\", shopId)\n        ]);\n        if (dataResult.error) {\n            throw new Error(\"Failed to fetch reviews: \".concat(dataResult.error.message));\n        }\n        return {\n            reviews: dataResult.data || [],\n            total: countResult.count || 0\n        };\n    }\n    /**\n   * Follow/unfollow shop\n   */ static async toggleShopFollow(shopId, userId) {\n        // Check if already following\n        const { data: existing } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_FOLLOWERS).select(\"id\").eq(\"shop_id\", shopId).eq(\"user_id\", userId).single();\n        if (existing) {\n            // Unfollow\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_FOLLOWERS).delete().eq(\"shop_id\", shopId).eq(\"user_id\", userId);\n            if (error) {\n                throw new Error(\"Failed to unfollow shop: \".concat(error.message));\n            }\n            return false;\n        } else {\n            // Follow\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_FOLLOWERS).insert({\n                shop_id: shopId,\n                user_id: userId\n            });\n            if (error) {\n                throw new Error(\"Failed to follow shop: \".concat(error.message));\n            }\n            return true;\n        }\n    }\n    /**\n   * Check if user is following shop\n   */ static async isFollowingShop(shopId, userId) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_FOLLOWERS).select(\"id\").eq(\"shop_id\", shopId).eq(\"user_id\", userId).single();\n        return !error && !!data;\n    }\n    /**\n   * Get shop followers count\n   */ static async getShopFollowersCount(shopId) {\n        try {\n            const { count, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_FOLLOWERS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"shop_id\", shopId);\n            if (error) {\n                console.warn(\"Failed to get followers count for shop \".concat(shopId, \":\"), error.message);\n                return 0;\n            }\n            return count || 0;\n        } catch (error) {\n            console.warn(\"Error getting followers count for shop \".concat(shopId, \":\"), error);\n            return 0;\n        }\n    }\n    /**\n   * Search shops\n   */ static async searchShops(query) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n        const offset = (page - 1) * limit;\n        const searchTerm = \"%\".concat(query, \"%\");\n        const [dataResult, countResult] = await Promise.all([\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"\\n          *,\\n          user:\".concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS, \"!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)\\n        \")).eq(\"status\", \"approved\").or(\"name.ilike.\".concat(searchTerm, \",description.ilike.\").concat(searchTerm)).order(\"is_featured\", {\n                ascending: false\n            }).order(\"rating\", {\n                ascending: false\n            }).range(offset, offset + limit - 1),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"approved\").or(\"name.ilike.\".concat(searchTerm, \",description.ilike.\").concat(searchTerm))\n        ]);\n        if (dataResult.error) {\n            throw new Error(\"Failed to search shops: \".concat(dataResult.error.message));\n        }\n        return {\n            shops: dataResult.data || [],\n            total: countResult.count || 0\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc2VydmljZXMvdmVuZG9yU2hvcHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUQ7QUFHakQ7Ozs7Ozs7O0NBUUMsR0FDTSxNQUFNRTtJQUNYOztHQUVDLEdBQ0QsYUFBYUMsV0FBV0MsUUFBOEgsRUFBdUI7UUFDM0ssSUFBSTtnQkFLR0EsZ0JBSUFBO1lBUkwsSUFBSSxDQUFDQSxTQUFTQyxPQUFPLEVBQUU7Z0JBQ3JCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLElBQUksR0FBQ0YsaUJBQUFBLFNBQVNHLElBQUksY0FBYkgscUNBQUFBLGVBQWVJLElBQUksS0FBSTtnQkFDMUIsTUFBTSxJQUFJRixNQUFNO1lBQ2xCO1lBRUEsSUFBSSxHQUFDRix3QkFBQUEsU0FBU0ssV0FBVyxjQUFwQkwsNENBQUFBLHNCQUFzQkksSUFBSSxLQUFJO2dCQUNqQyxNQUFNLElBQUlGLE1BQU07WUFDbEI7WUFFQSxtQ0FBbUM7WUFDbkMsTUFBTUksbUJBQW1CLE1BQU0sSUFBSSxDQUFDQyxXQUFXLENBQUNQLFNBQVNDLE9BQU87WUFDaEUsSUFBSUssa0JBQWtCO2dCQUNwQixNQUFNLElBQUlKLE1BQU07WUFDbEI7WUFFQU0sUUFBUUMsR0FBRyxDQUFDLDJCQUFzRFQsT0FBM0JBLFNBQVNDLE9BQU8sRUFBQyxZQUF3QixPQUFkRCxTQUFTRyxJQUFJO1lBRS9FLDBCQUEwQjtZQUMxQixNQUFNTyxPQUFPVixTQUFTRyxJQUFJLENBQUNRLFdBQVcsR0FDbkNDLE9BQU8sQ0FBQyxlQUFlLEtBQ3ZCQSxPQUFPLENBQUMsWUFBWTtZQUV2QiwrQkFBK0I7WUFDL0IsTUFBTSxFQUFFQyxNQUFNQyxnQkFBZ0IsRUFBRSxHQUFHLE1BQU1sQixtREFBUUEsQ0FDOUNtQixJQUFJLENBQUNsQixpREFBTUEsQ0FBQ21CLFlBQVksRUFDeEJDLE1BQU0sQ0FBQyxNQUNQQyxFQUFFLENBQUMsUUFBUSxHQUFXQyxPQUFSVCxNQUFLLEtBQWMsT0FBWFMsS0FBS0MsR0FBRyxLQUM5QkMsTUFBTTtZQUVULE1BQU1DLFlBQVlSLG1CQUFtQixHQUFXSyxPQUFSVCxNQUFLLEtBQWlCYSxPQUFkSixLQUFLQyxHQUFHLElBQUcsS0FBMkMsT0FBeENHLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHLE1BQU8sR0FBV1AsT0FBUlQsTUFBSyxLQUFjLE9BQVhTLEtBQUtDLEdBQUc7WUFFN0gsTUFBTSxFQUFFUCxJQUFJLEVBQUVjLEtBQUssRUFBRSxHQUFHLE1BQU0vQixtREFBUUEsQ0FDbkNtQixJQUFJLENBQUNsQixpREFBTUEsQ0FBQ21CLFlBQVksRUFDeEJZLE1BQU0sQ0FBQztnQkFDTixHQUFHNUIsUUFBUTtnQkFDWEcsTUFBTUgsU0FBU0csSUFBSSxDQUFDQyxJQUFJO2dCQUN4QkMsYUFBYUwsU0FBU0ssV0FBVyxDQUFDRCxJQUFJO2dCQUN0Q00sTUFBTVk7Z0JBQ05PLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFFBQVE7Z0JBQ1JDLGVBQWU7Z0JBQ2ZDLGdCQUFnQjtnQkFDaEJDLGFBQWE7WUFDZixHQUNDakIsTUFBTSxDQUFFLDhHQUlSSSxNQUFNO1lBRVQsSUFBSU0sT0FBTztnQkFDVG5CLFFBQVFtQixLQUFLLENBQUMsd0JBQXdCQTtnQkFDdEMsTUFBTSxJQUFJekIsTUFBTSwwQkFBd0MsT0FBZHlCLE1BQU1RLE9BQU87WUFDekQ7WUFFQSxJQUFJLENBQUN0QixNQUFNO2dCQUNULE1BQU0sSUFBSVgsTUFBTTtZQUNsQjtZQUVBTSxRQUFRQyxHQUFHLENBQUMsOEJBQThCSSxLQUFLdUIsRUFBRTtZQUNqRCxPQUFPdkI7UUFDVCxFQUFFLE9BQU9jLE9BQU87WUFDZG5CLFFBQVFtQixLQUFLLENBQUMsdUNBQXVDQTtZQUNyRCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFVLGNBQWMzQixJQUFZLEVBQThCO1FBQ25FLElBQUk7WUFDRixJQUFJLEVBQUNBLGlCQUFBQSwyQkFBQUEsS0FBTU4sSUFBSSxLQUFJO2dCQUNqQixNQUFNLElBQUlGLE1BQU07WUFDbEI7WUFFQU0sUUFBUUMsR0FBRyxDQUFDLDBCQUErQixPQUFMQztZQUV0QyxNQUFNLEVBQUVHLElBQUksRUFBRWMsS0FBSyxFQUFFLEdBQUcsTUFBTS9CLG1EQUFRQSxDQUNuQ21CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDbUIsWUFBWSxFQUN4QkMsTUFBTSxDQUFFLDhHQUlSQyxFQUFFLENBQUMsUUFBUVIsS0FBS04sSUFBSSxJQUNwQmlCLE1BQU07WUFFVCxJQUFJTSxPQUFPO2dCQUNULElBQUlBLE1BQU1XLElBQUksS0FBSyxZQUFZO29CQUM3QjlCLFFBQVFDLEdBQUcsQ0FBQyw2QkFBa0MsT0FBTEM7b0JBQ3pDLE9BQU8sS0FBSyxZQUFZOztnQkFDMUI7Z0JBQ0FGLFFBQVFtQixLQUFLLENBQUMsZ0NBQWdDQTtnQkFDOUMsTUFBTSxJQUFJekIsTUFBTSx5QkFBdUMsT0FBZHlCLE1BQU1RLE9BQU87WUFDeEQ7WUFFQSxJQUFJdEIsTUFBTTtnQkFDUkwsUUFBUUMsR0FBRyxDQUFDLGVBQXFDQyxPQUF0QkcsS0FBS3VCLEVBQUUsRUFBQyxnQkFBbUIsT0FBTDFCO1lBQ25EO1lBRUEsT0FBT0c7UUFDVCxFQUFFLE9BQU9jLE9BQU87WUFDZG5CLFFBQVFtQixLQUFLLENBQUMsMENBQTBDQTtZQUN4RCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFwQixZQUFZZ0MsTUFBYyxFQUE4QjtRQUNuRSxJQUFJO1lBQ0YsSUFBSSxDQUFDQSxRQUFRO2dCQUNYLE1BQU0sSUFBSXJDLE1BQU07WUFDbEI7WUFFQU0sUUFBUUMsR0FBRyxDQUFDLDJCQUFrQyxPQUFQOEI7WUFFdkMsTUFBTSxFQUFFMUIsSUFBSSxFQUFFYyxLQUFLLEVBQUUsR0FBRyxNQUFNL0IsbURBQVFBLENBQ25DbUIsSUFBSSxDQUFDbEIsaURBQU1BLENBQUNtQixZQUFZLEVBQ3hCQyxNQUFNLENBQUUsOEdBSVJDLEVBQUUsQ0FBQyxXQUFXcUIsUUFDZGxCLE1BQU07WUFFVCxJQUFJTSxTQUFTQSxNQUFNVyxJQUFJLEtBQUssWUFBWTtnQkFDdEM5QixRQUFRbUIsS0FBSyxDQUFDLDZCQUE2QkE7Z0JBQzNDLE1BQU0sSUFBSXpCLE1BQU0sOEJBQTRDLE9BQWR5QixNQUFNUSxPQUFPO1lBQzdEO1lBRUEsSUFBSXRCLE1BQU07Z0JBQ1JMLFFBQVFDLEdBQUcsQ0FBQyx1QkFBa0NJLE9BQVgwQixRQUFPLE1BQVksT0FBUjFCLEtBQUt1QixFQUFFO1lBQ3ZELE9BQU87Z0JBQ0w1QixRQUFRQyxHQUFHLENBQUMsMkJBQWtDLE9BQVA4QjtZQUN6QztZQUVBLE9BQU8xQixRQUFRO1FBQ2pCLEVBQUUsT0FBT2MsT0FBTztZQUNkbkIsUUFBUW1CLEtBQUssQ0FBQyx3Q0FBd0NBO1lBQ3RELE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYWEsbUJBR1Y7WUFIMkJDLE9BQUFBLGlFQUFlLEdBQUdDLFFBQUFBLGlFQUFnQjtRQUk5RCxJQUFJO2dCQWdDa0NDO1lBL0JwQyxNQUFNQyxTQUFTLENBQUNILE9BQU8sS0FBS0M7WUFFNUJsQyxRQUFRQyxHQUFHLENBQUMsbUNBQW1EaUMsT0FBaEJELE1BQUssYUFBaUIsT0FBTkM7WUFFL0QsTUFBTSxDQUFDQyxZQUFZRSxZQUFZLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO2dCQUNsRG5ELG1EQUFRQSxDQUNMbUIsSUFBSSxDQUFDbEIsaURBQU1BLENBQUNtQixZQUFZLEVBQ3hCQyxNQUFNLENBQUUsb0hBSVJDLEVBQUUsQ0FBQyxVQUFVLFlBQ2I4QixLQUFLLENBQUMsZUFBZTtvQkFBRUMsV0FBVztnQkFBTSxHQUN4Q0QsS0FBSyxDQUFDLGNBQWM7b0JBQUVDLFdBQVc7Z0JBQU0sR0FDdkNDLEtBQUssQ0FBQ04sUUFBUUEsU0FBU0YsUUFBUTtnQkFDbEM5QyxtREFBUUEsQ0FDTG1CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDbUIsWUFBWSxFQUN4QkMsTUFBTSxDQUFDLEtBQUs7b0JBQUVrQyxPQUFPO29CQUFTQyxNQUFNO2dCQUFLLEdBQ3pDbEMsRUFBRSxDQUFDLFVBQVU7YUFDakI7WUFFRCxJQUFJeUIsV0FBV2hCLEtBQUssRUFBRTtnQkFDcEJuQixRQUFRbUIsS0FBSyxDQUFDLGtDQUFrQ2dCLFdBQVdoQixLQUFLO2dCQUNoRSxNQUFNLElBQUl6QixNQUFNLDBCQUFtRCxPQUF6QnlDLFdBQVdoQixLQUFLLENBQUNRLE9BQU87WUFDcEU7WUFFQSxJQUFJVSxZQUFZbEIsS0FBSyxFQUFFO2dCQUNyQm5CLFFBQVFtQixLQUFLLENBQUMsd0NBQXdDa0IsWUFBWWxCLEtBQUs7Z0JBQ3ZFLE1BQU0sSUFBSXpCLE1BQU0sZ0NBQTBELE9BQTFCMkMsWUFBWWxCLEtBQUssQ0FBQ1EsT0FBTztZQUMzRTtZQUVBM0IsUUFBUUMsR0FBRyxDQUFDLHdCQUErRW9DLE9BQXZERixFQUFBQSxtQkFBQUEsV0FBVzlCLElBQUksY0FBZjhCLHVDQUFBQSxpQkFBaUJVLE1BQU0sS0FBSSxHQUFFLDRCQUFpRCxPQUF2QlIsWUFBWU0sS0FBSyxJQUFJLEdBQUU7WUFFbEgsT0FBTztnQkFDTEcsT0FBT1gsV0FBVzlCLElBQUksSUFBSSxFQUFFO2dCQUM1QjBDLE9BQU9WLFlBQVlNLEtBQUssSUFBSTtZQUM5QjtRQUNGLEVBQUUsT0FBT3hCLE9BQU87WUFDZG5CLFFBQVFtQixLQUFLLENBQUMsNkNBQTZDQTtZQUMzRCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWE2QixXQWFWO1lBWkRDLFVBQUFBLGlFQU1JLENBQUMsR0FDTGhCLE9BQUFBLGlFQUFlLEdBQ2ZDLFFBQUFBLGlFQUFnQjtRQUtoQixJQUFJO2dCQTZCRWUsaUJBTUFBLG1CQW9CZ0NkO1lBdERwQyxNQUFNQyxTQUFTLENBQUNILE9BQU8sS0FBS0M7WUFFNUJsQyxRQUFRQyxHQUFHLENBQUUsZ0NBQStCZ0QsU0FBUyxTQUF5QmYsT0FBaEJELE1BQUssYUFBaUIsT0FBTkM7WUFFOUUsSUFBSWdCLFFBQVE5RCxtREFBUUEsQ0FDakJtQixJQUFJLENBQUNsQixpREFBTUEsQ0FBQ21CLFlBQVksRUFDeEJDLE1BQU0sQ0FBRSw4R0FJUitCLEtBQUssQ0FBQyxlQUFlO2dCQUFFQyxXQUFXO1lBQU0sR0FDeENELEtBQUssQ0FBQyxjQUFjO2dCQUFFQyxXQUFXO1lBQU07WUFFMUMsSUFBSVUsYUFBYS9ELG1EQUFRQSxDQUN0Qm1CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDbUIsWUFBWSxFQUN4QkMsTUFBTSxDQUFDLEtBQUs7Z0JBQUVrQyxPQUFPO2dCQUFTQyxNQUFNO1lBQUs7WUFFNUMsZ0JBQWdCO1lBQ2hCLElBQUlLLFFBQVE1QixNQUFNLEVBQUU7Z0JBQ2xCNkIsUUFBUUEsTUFBTXhDLEVBQUUsQ0FBQyxVQUFVdUMsUUFBUTVCLE1BQU07Z0JBQ3pDOEIsYUFBYUEsV0FBV3pDLEVBQUUsQ0FBQyxVQUFVdUMsUUFBUTVCLE1BQU07WUFDckQ7WUFFQSxJQUFJNEIsUUFBUUcsUUFBUSxLQUFLQyxXQUFXO2dCQUNsQ0gsUUFBUUEsTUFBTXhDLEVBQUUsQ0FBQyxlQUFldUMsUUFBUUcsUUFBUTtnQkFDaERELGFBQWFBLFdBQVd6QyxFQUFFLENBQUMsZUFBZXVDLFFBQVFHLFFBQVE7WUFDNUQ7WUFFQSxLQUFJSCxrQkFBQUEsUUFBUUssTUFBTSxjQUFkTCxzQ0FBQUEsZ0JBQWdCckQsSUFBSSxJQUFJO2dCQUMxQixNQUFNMkQsYUFBYSxJQUEwQixPQUF0Qk4sUUFBUUssTUFBTSxDQUFDMUQsSUFBSSxJQUFHO2dCQUM3Q3NELFFBQVFBLE1BQU1NLEVBQUUsQ0FBQyxjQUE4Q0QsT0FBaENBLFlBQVcsdUJBQWdDLE9BQVhBO2dCQUMvREosYUFBYUEsV0FBV0ssRUFBRSxDQUFDLGNBQThDRCxPQUFoQ0EsWUFBVyx1QkFBZ0MsT0FBWEE7WUFDM0U7WUFFQSxLQUFJTixvQkFBQUEsUUFBUVEsUUFBUSxjQUFoQlIsd0NBQUFBLGtCQUFrQnJELElBQUksSUFBSTtnQkFDNUJzRCxRQUFRQSxNQUFNUSxLQUFLLENBQUMsV0FBVyxJQUE0QixPQUF4QlQsUUFBUVEsUUFBUSxDQUFDN0QsSUFBSSxJQUFHO2dCQUMzRHVELGFBQWFBLFdBQVdPLEtBQUssQ0FBQyxXQUFXLElBQTRCLE9BQXhCVCxRQUFRUSxRQUFRLENBQUM3RCxJQUFJLElBQUc7WUFDdkU7WUFFQSxNQUFNLENBQUN1QyxZQUFZRSxZQUFZLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO2dCQUNsRFcsTUFBTVIsS0FBSyxDQUFDTixRQUFRQSxTQUFTRixRQUFRO2dCQUNyQ2lCO2FBQ0Q7WUFFRCxJQUFJaEIsV0FBV2hCLEtBQUssRUFBRTtnQkFDcEJuQixRQUFRbUIsS0FBSyxDQUFDLHlCQUF5QmdCLFdBQVdoQixLQUFLO2dCQUN2RCxNQUFNLElBQUl6QixNQUFNLDBCQUFtRCxPQUF6QnlDLFdBQVdoQixLQUFLLENBQUNRLE9BQU87WUFDcEU7WUFFQSxJQUFJVSxZQUFZbEIsS0FBSyxFQUFFO2dCQUNyQm5CLFFBQVFtQixLQUFLLENBQUMsK0JBQStCa0IsWUFBWWxCLEtBQUs7Z0JBQzlELE1BQU0sSUFBSXpCLE1BQU0sZ0NBQTBELE9BQTFCMkMsWUFBWWxCLEtBQUssQ0FBQ1EsT0FBTztZQUMzRTtZQUVBM0IsUUFBUUMsR0FBRyxDQUFDLHdCQUFzRW9DLE9BQTlDRixFQUFBQSxtQkFBQUEsV0FBVzlCLElBQUksY0FBZjhCLHVDQUFBQSxpQkFBaUJVLE1BQU0sS0FBSSxHQUFFLG1CQUF3QyxPQUF2QlIsWUFBWU0sS0FBSyxJQUFJLEdBQUU7WUFFekcsT0FBTztnQkFDTEcsT0FBT1gsV0FBVzlCLElBQUksSUFBSSxFQUFFO2dCQUM1QjBDLE9BQU9WLFlBQVlNLEtBQUssSUFBSTtZQUM5QjtRQUNGLEVBQUUsT0FBT3hCLE9BQU87WUFDZG5CLFFBQVFtQixLQUFLLENBQUMscUNBQXFDQTtZQUNuRCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWF3QyxhQUFhNUIsTUFBYyxFQUF5QjtRQUMvRCxJQUFJO1lBQ0YsSUFBSSxDQUFDQSxRQUFRO2dCQUNYLE1BQU0sSUFBSXJDLE1BQU07WUFDbEI7WUFFQU0sUUFBUUMsR0FBRyxDQUFDLDRCQUFtQyxPQUFQOEI7WUFFeEMsTUFBTSxFQUFFMUIsSUFBSSxFQUFFYyxLQUFLLEVBQUUsR0FBRyxNQUFNL0IsbURBQVFBLENBQ25DbUIsSUFBSSxDQUFDbEIsaURBQU1BLENBQUNtQixZQUFZLEVBQ3hCQyxNQUFNLENBQUUsOEdBSVJDLEVBQUUsQ0FBQyxXQUFXcUIsUUFDZFMsS0FBSyxDQUFDLGNBQWM7Z0JBQUVDLFdBQVc7WUFBTTtZQUUxQyxJQUFJdEIsT0FBTztnQkFDVG5CLFFBQVFtQixLQUFLLENBQUMsOEJBQThCQTtnQkFDNUMsTUFBTSxJQUFJekIsTUFBTSwrQkFBNkMsT0FBZHlCLE1BQU1RLE9BQU87WUFDOUQ7WUFFQTNCLFFBQVFDLEdBQUcsQ0FBQyx3QkFBNkQ4QixPQUFyQzFCLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXdDLE1BQU0sS0FBSSxHQUFFLHFCQUEwQixPQUFQZDtZQUV6RSxPQUFPMUIsUUFBUSxFQUFFO1FBQ25CLEVBQUUsT0FBT2MsT0FBTztZQUNkbkIsUUFBUW1CLEtBQUssQ0FBQyx5Q0FBeUNBO1lBQ3ZELE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYXlDLFdBQVdDLE1BQWMsRUFBRUMsT0FBNEIsRUFBdUI7UUFDekYsTUFBTSxFQUFFekQsSUFBSSxFQUFFYyxLQUFLLEVBQUUsR0FBRyxNQUFNL0IsbURBQVFBLENBQ25DbUIsSUFBSSxDQUFDbEIsaURBQU1BLENBQUNtQixZQUFZLEVBQ3hCdUQsTUFBTSxDQUFDRCxTQUNQcEQsRUFBRSxDQUFDLE1BQU1tRCxRQUNUcEQsTUFBTSxHQUNOSSxNQUFNO1FBRVQsSUFBSU0sT0FBTztZQUNULE1BQU0sSUFBSXpCLE1BQU0sMEJBQXdDLE9BQWR5QixNQUFNUSxPQUFPO1FBQ3pEO1FBRUEsT0FBT3RCO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWEyRCwyQkFBMEM7UUFDckQsSUFBSTtZQUNGaEUsUUFBUUMsR0FBRyxDQUFDO1lBRVoseUJBQXlCO1lBQ3pCLE1BQU0sRUFBRUksTUFBTXlDLEtBQUssRUFBRTNCLEtBQUssRUFBRSxHQUFHLE1BQU0vQixtREFBUUEsQ0FDMUNtQixJQUFJLENBQUNsQixpREFBTUEsQ0FBQ21CLFlBQVksRUFDeEJDLE1BQU0sQ0FBQyxZQUNQQyxFQUFFLENBQUMsVUFBVTtZQUVoQixJQUFJUyxPQUFPO2dCQUNULE1BQU0sSUFBSXpCLE1BQU0sMEJBQXdDLE9BQWR5QixNQUFNUSxPQUFPO1lBQ3pEO1lBRUEsSUFBSSxDQUFDbUIsU0FBU0EsTUFBTUQsTUFBTSxLQUFLLEdBQUc7Z0JBQ2hDN0MsUUFBUUMsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxtQ0FBbUM7WUFDbkMsS0FBSyxNQUFNZ0UsUUFBUW5CLE1BQU87Z0JBQ3hCLElBQUk7b0JBQ0YsTUFBTSxJQUFJLENBQUNvQixxQkFBcUIsQ0FBQ0QsS0FBS3JDLEVBQUU7b0JBQ3hDNUIsUUFBUUMsR0FBRyxDQUFDLGtDQUE0QyxPQUFWZ0UsS0FBS3RFLElBQUk7Z0JBQ3pELEVBQUUsT0FBT3dCLE9BQU87b0JBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLDBDQUFvRCxPQUFWOEMsS0FBS3RFLElBQUksRUFBQyxNQUFJd0I7Z0JBQ3hFO1lBQ0Y7WUFFQW5CLFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBT2tCLE9BQU87WUFDZG5CLFFBQVFtQixLQUFLLENBQUMseUNBQXlDQTtZQUN2RCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWErQyxzQkFBc0JMLE1BQWMsRUFBdUI7UUFDdEUsSUFBSTtZQUNGN0QsUUFBUUMsR0FBRyxDQUFDLG1DQUEwQyxPQUFQNEQ7WUFFL0MsMENBQTBDO1lBQzFDLE1BQU0sRUFBRXhELE1BQU04RCxZQUFZLEVBQUVoRCxPQUFPaUQsYUFBYSxFQUFFLEdBQUcsTUFBTWhGLG1EQUFRQSxDQUNoRW1CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDZ0YsYUFBYSxFQUN6QjVELE1BQU0sQ0FBQyxNQUNQQyxFQUFFLENBQUMsV0FBV21EO1lBRWpCLElBQUlPLGVBQWU7Z0JBQ2pCcEUsUUFBUW1CLEtBQUssQ0FBQyxpQ0FBaUNpRDtnQkFDL0MsTUFBTSxJQUFJMUUsTUFBTSxrQ0FBd0QsT0FBdEIwRSxjQUFjekMsT0FBTztZQUN6RTtZQUVBLE1BQU0yQyxhQUFhSCxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNJLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRTVDLEVBQUUsTUFBSyxFQUFFO1lBRXJELDRFQUE0RTtZQUM1RSxNQUFNLENBQUM2QyxnQkFBZ0JDLGVBQWVDLGNBQWNDLFlBQVksR0FBRyxNQUFNdEMsUUFBUXVDLFVBQVUsQ0FBQztnQkFDMUYsaUJBQWlCO2dCQUNqQnpGLG1EQUFRQSxDQUNMbUIsSUFBSSxDQUFDbEIsaURBQU1BLENBQUNnRixhQUFhLEVBQ3pCNUQsTUFBTSxDQUFDLEtBQUs7b0JBQUVrQyxPQUFPO29CQUFTQyxNQUFNO2dCQUFLLEdBQ3pDbEMsRUFBRSxDQUFDLFdBQVdtRDtnQkFFakIsNkNBQTZDO2dCQUM3Q1MsV0FBV3pCLE1BQU0sR0FBRyxJQUNoQnpELG1EQUFRQSxDQUNMbUIsSUFBSSxDQUFDbEIsaURBQU1BLENBQUN5RixlQUFlLEVBQzNCckUsTUFBTSxDQUFDLFVBQ1BzRSxFQUFFLENBQUMsY0FBY1QsY0FDcEJoQyxRQUFRMEMsT0FBTyxDQUFDO29CQUFFM0UsTUFBTSxFQUFFO29CQUFFYyxPQUFPO2dCQUFLO2dCQUU1Qyx3QkFBd0I7Z0JBQ3hCL0IsbURBQVFBLENBQ0xtQixJQUFJLENBQUNsQixpREFBTUEsQ0FBQzRGLFdBQVcsRUFDdkJ4RSxNQUFNLENBQUMsS0FBSztvQkFBRWtDLE9BQU87b0JBQVNDLE1BQU07Z0JBQUssR0FDekNsQyxFQUFFLENBQUMsV0FBV21ELFFBQ2RrQixFQUFFLENBQUMsVUFBVTtvQkFBQztvQkFBYTtpQkFBWTtnQkFFMUMsMENBQTBDO2dCQUMxQzNGLG1EQUFRQSxDQUNMbUIsSUFBSSxDQUFDbEIsaURBQU1BLENBQUNnRixhQUFhLEVBQ3pCNUQsTUFBTSxDQUFDLFNBQ1BDLEVBQUUsQ0FBQyxXQUFXbUQ7YUFDbEI7WUFFRCxzQ0FBc0M7WUFDdEMsTUFBTXFCLGdCQUFnQlQsZUFBZXBELE1BQU0sS0FBSyxlQUFlLENBQUNvRCxlQUFlVSxLQUFLLENBQUNoRSxLQUFLLEdBQ3RGc0QsZUFBZVUsS0FBSyxDQUFDeEMsS0FBSyxJQUFJLElBQzlCO1lBRUosTUFBTXlDLFVBQVVWLGNBQWNyRCxNQUFNLEtBQUssZUFBZSxDQUFDcUQsY0FBY1MsS0FBSyxDQUFDaEUsS0FBSyxHQUM5RXVELGNBQWNTLEtBQUssQ0FBQzlFLElBQUksSUFBSSxFQUFFLEdBQzlCLEVBQUU7WUFDTixNQUFNZ0YsZUFBZUQsUUFBUXZDLE1BQU07WUFDbkMsTUFBTXlDLGdCQUFnQkQsZUFBZSxJQUNqQ0QsUUFBUUcsTUFBTSxDQUFDLENBQUNDLEtBQUtDLFNBQVdELE1BQU1DLE9BQU9sRSxNQUFNLEVBQUUsS0FBSzhELGVBQzFEO1lBRUosTUFBTUssYUFBYWYsYUFBYXRELE1BQU0sS0FBSyxlQUFlLENBQUNzRCxhQUFhUSxLQUFLLENBQUNoRSxLQUFLLEdBQy9Fd0QsYUFBYVEsS0FBSyxDQUFDeEMsS0FBSyxJQUFJLElBQzVCO1lBRUosTUFBTWdELGVBQWVmLFlBQVl2RCxNQUFNLEtBQUssZUFBZSxDQUFDdUQsWUFBWU8sS0FBSyxDQUFDaEUsS0FBSyxHQUMvRXlELFlBQVlPLEtBQUssQ0FBQzlFLElBQUksSUFBSSxFQUFFLEdBQzVCLEVBQUU7WUFDTixNQUFNdUYsYUFBYUQsYUFBYUosTUFBTSxDQUFDLENBQUNDLEtBQUtLLFVBQVlMLE1BQU9LLENBQUFBLFFBQVFDLEtBQUssSUFBSSxJQUFJO1lBRXJGLGtDQUFrQztZQUNsQyxJQUFJckIsZUFBZXBELE1BQU0sS0FBSyxZQUFZO2dCQUN4Q3JCLFFBQVErRixJQUFJLENBQUMsaUNBQWlDdEIsZUFBZXVCLE1BQU07WUFDckU7WUFDQSxJQUFJdEIsY0FBY3JELE1BQU0sS0FBSyxZQUFZO2dCQUN2Q3JCLFFBQVErRixJQUFJLENBQUMsMEJBQTBCckIsY0FBY3NCLE1BQU07WUFDN0Q7WUFDQSxJQUFJckIsYUFBYXRELE1BQU0sS0FBSyxZQUFZO2dCQUN0Q3JCLFFBQVErRixJQUFJLENBQUMsK0JBQStCcEIsYUFBYXFCLE1BQU07WUFDakU7WUFDQSxJQUFJcEIsWUFBWXZELE1BQU0sS0FBSyxZQUFZO2dCQUNyQ3JCLFFBQVErRixJQUFJLENBQUMsZ0NBQWdDbkIsWUFBWW9CLE1BQU07WUFDakU7WUFFQWhHLFFBQVFDLEdBQUcsQ0FBQyxRQUFlLE9BQVA0RCxRQUFPLGlCQUFlO2dCQUN4Q3FCO2dCQUNBRztnQkFDQUM7Z0JBQ0FJO2dCQUNBRTtZQUNGO1lBRUEsNkNBQTZDO1lBQzdDLE1BQU0sRUFBRXZGLElBQUksRUFBRWMsS0FBSyxFQUFFLEdBQUcsTUFBTS9CLG1EQUFRQSxDQUNuQ21CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDbUIsWUFBWSxFQUN4QnVELE1BQU0sQ0FBQztnQkFDTnRDLGdCQUFnQnlEO2dCQUNoQjFELGVBQWU2RDtnQkFDZjlELFFBQVEwRSxPQUFPWCxjQUFjWSxPQUFPLENBQUM7Z0JBQ3JDeEUsYUFBYWdFO2dCQUNiUyxhQUFhUDtnQkFDYlEsWUFBWSxJQUFJekYsT0FBTzBGLFdBQVc7WUFDcEMsR0FDQzNGLEVBQUUsQ0FBQyxNQUFNbUQsUUFDVHBELE1BQU07WUFFVCxJQUFJVSxPQUFPO2dCQUNULE1BQU0sSUFBSXpCLE1BQU0scUNBQW1ELE9BQWR5QixNQUFNUSxPQUFPO1lBQ3BFO1lBRUEsSUFBSSxDQUFDdEIsUUFBUUEsS0FBS3dDLE1BQU0sS0FBSyxHQUFHO2dCQUM5QixNQUFNLElBQUluRCxNQUFNLG1CQUEwQixPQUFQbUU7WUFDckM7WUFFQTdELFFBQVFDLEdBQUcsQ0FBQyw2Q0FBb0QsT0FBUDREO1lBQ3pELE9BQU94RCxJQUFJLENBQUMsRUFBRTtRQUNoQixFQUFFLE9BQU9jLE9BQU87WUFDZG5CLFFBQVFtQixLQUFLLENBQUMscUNBQXFDQTtZQUNuRCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFtRixpQkFBaUJDLFdBQWtFLEVBQXdCO1FBQ3RILE1BQU0sRUFBRWxHLElBQUksRUFBRWMsS0FBSyxFQUFFLEdBQUcsTUFBTS9CLG1EQUFRQSxDQUNuQ21CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDZ0YsYUFBYSxFQUN6QmpELE1BQU0sQ0FBQ21GLGFBQ1A5RixNQUFNLEdBQ05JLE1BQU07UUFFVCxJQUFJTSxPQUFPO1lBQ1QsTUFBTSxJQUFJekIsTUFBTSxrQ0FBZ0QsT0FBZHlCLE1BQU1RLE9BQU87UUFDakU7UUFFQSxPQUFPdEI7SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYW1HLGdCQUFnQjNDLE1BQWMsRUFHeEM7WUFIMEM1QixPQUFBQSxpRUFBZSxHQUFHQyxRQUFBQSxpRUFBZ0I7UUFJN0Usa0VBQWtFO1FBQ2xFLE1BQU0sRUFBRXVFLGtCQUFrQixFQUFFLEdBQUcsTUFBTSxnT0FBTztRQUM1QyxPQUFPQSxtQkFBbUJELGVBQWUsQ0FBQzNDLFFBQVE1QixNQUFNQztJQUMxRDtJQUVBOztHQUVDLEdBQ0QsYUFBYXdFLGNBQWNDLFVBQWdFLEVBQXVCO1FBQ2hILE1BQU0sRUFBRXRHLElBQUksRUFBRWMsS0FBSyxFQUFFLEdBQUcsTUFBTS9CLG1EQUFRQSxDQUNuQ21CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDdUgsWUFBWSxFQUN4QnhGLE1BQU0sQ0FBQ3VGLFlBQ1BsRyxNQUFNLEdBQ05JLE1BQU07UUFFVCxJQUFJTSxPQUFPO1lBQ1QsTUFBTSxJQUFJekIsTUFBTSx5QkFBdUMsT0FBZHlCLE1BQU1RLE9BQU87UUFDeEQ7UUFFQSxPQUFPdEI7SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYXdHLGVBQWVoRCxNQUFjLEVBR3ZDO1lBSHlDNUIsT0FBQUEsaUVBQWUsR0FBR0MsUUFBQUEsaUVBQWdCO1FBSTVFLE1BQU1FLFNBQVMsQ0FBQ0gsT0FBTyxLQUFLQztRQUU1QixNQUFNLENBQUNDLFlBQVlFLFlBQVksR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7WUFDbERuRCxtREFBUUEsQ0FDTG1CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDdUgsWUFBWSxFQUN4Qm5HLE1BQU0sQ0FBQyxrQ0FFYyxPQUFicEIsaURBQU1BLENBQUN5SCxLQUFLLEVBQUMsb0VBRXJCcEcsRUFBRSxDQUFDLFdBQVdtRCxRQUNkckIsS0FBSyxDQUFDLGNBQWM7Z0JBQUVDLFdBQVc7WUFBTSxHQUN2Q0MsS0FBSyxDQUFDTixRQUFRQSxTQUFTRixRQUFRO1lBQ2xDOUMsbURBQVFBLENBQ0xtQixJQUFJLENBQUNsQixpREFBTUEsQ0FBQ3VILFlBQVksRUFDeEJuRyxNQUFNLENBQUMsS0FBSztnQkFBRWtDLE9BQU87Z0JBQVNDLE1BQU07WUFBSyxHQUN6Q2xDLEVBQUUsQ0FBQyxXQUFXbUQ7U0FDbEI7UUFFRCxJQUFJMUIsV0FBV2hCLEtBQUssRUFBRTtZQUNwQixNQUFNLElBQUl6QixNQUFNLDRCQUFxRCxPQUF6QnlDLFdBQVdoQixLQUFLLENBQUNRLE9BQU87UUFDdEU7UUFFQSxPQUFPO1lBQ0x5RCxTQUFTakQsV0FBVzlCLElBQUksSUFBSSxFQUFFO1lBQzlCMEMsT0FBT1YsWUFBWU0sS0FBSyxJQUFJO1FBQzlCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFvRSxpQkFBaUJsRCxNQUFjLEVBQUU5QixNQUFjLEVBQW9CO1FBQzlFLDZCQUE2QjtRQUM3QixNQUFNLEVBQUUxQixNQUFNMkcsUUFBUSxFQUFFLEdBQUcsTUFBTTVILG1EQUFRQSxDQUN0Q21CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDNEgsY0FBYyxFQUMxQnhHLE1BQU0sQ0FBQyxNQUNQQyxFQUFFLENBQUMsV0FBV21ELFFBQ2RuRCxFQUFFLENBQUMsV0FBV3FCLFFBQ2RsQixNQUFNO1FBRVQsSUFBSW1HLFVBQVU7WUFDWixXQUFXO1lBQ1gsTUFBTSxFQUFFN0YsS0FBSyxFQUFFLEdBQUcsTUFBTS9CLG1EQUFRQSxDQUM3Qm1CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDNEgsY0FBYyxFQUMxQkMsTUFBTSxHQUNOeEcsRUFBRSxDQUFDLFdBQVdtRCxRQUNkbkQsRUFBRSxDQUFDLFdBQVdxQjtZQUVqQixJQUFJWixPQUFPO2dCQUNULE1BQU0sSUFBSXpCLE1BQU0sNEJBQTBDLE9BQWR5QixNQUFNUSxPQUFPO1lBQzNEO1lBQ0EsT0FBTztRQUNULE9BQU87WUFDTCxTQUFTO1lBQ1QsTUFBTSxFQUFFUixLQUFLLEVBQUUsR0FBRyxNQUFNL0IsbURBQVFBLENBQzdCbUIsSUFBSSxDQUFDbEIsaURBQU1BLENBQUM0SCxjQUFjLEVBQzFCN0YsTUFBTSxDQUFDO2dCQUFFK0YsU0FBU3REO2dCQUFRcEUsU0FBU3NDO1lBQU87WUFFN0MsSUFBSVosT0FBTztnQkFDVCxNQUFNLElBQUl6QixNQUFNLDBCQUF3QyxPQUFkeUIsTUFBTVEsT0FBTztZQUN6RDtZQUNBLE9BQU87UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFheUYsZ0JBQWdCdkQsTUFBYyxFQUFFOUIsTUFBYyxFQUFvQjtRQUM3RSxNQUFNLEVBQUUxQixJQUFJLEVBQUVjLEtBQUssRUFBRSxHQUFHLE1BQU0vQixtREFBUUEsQ0FDbkNtQixJQUFJLENBQUNsQixpREFBTUEsQ0FBQzRILGNBQWMsRUFDMUJ4RyxNQUFNLENBQUMsTUFDUEMsRUFBRSxDQUFDLFdBQVdtRCxRQUNkbkQsRUFBRSxDQUFDLFdBQVdxQixRQUNkbEIsTUFBTTtRQUVULE9BQU8sQ0FBQ00sU0FBUyxDQUFDLENBQUNkO0lBQ3JCO0lBRUE7O0dBRUMsR0FDRCxhQUFhZ0gsc0JBQXNCeEQsTUFBYyxFQUFtQjtRQUNsRSxJQUFJO1lBQ0YsTUFBTSxFQUFFbEIsS0FBSyxFQUFFeEIsS0FBSyxFQUFFLEdBQUcsTUFBTS9CLG1EQUFRQSxDQUNwQ21CLElBQUksQ0FBQ2xCLGlEQUFNQSxDQUFDNEgsY0FBYyxFQUMxQnhHLE1BQU0sQ0FBQyxLQUFLO2dCQUFFa0MsT0FBTztnQkFBU0MsTUFBTTtZQUFLLEdBQ3pDbEMsRUFBRSxDQUFDLFdBQVdtRDtZQUVqQixJQUFJMUMsT0FBTztnQkFDVG5CLFFBQVErRixJQUFJLENBQUMsMENBQWlELE9BQVBsQyxRQUFPLE1BQUkxQyxNQUFNUSxPQUFPO2dCQUMvRSxPQUFPO1lBQ1Q7WUFFQSxPQUFPZ0IsU0FBUztRQUNsQixFQUFFLE9BQU94QixPQUFPO1lBQ2RuQixRQUFRK0YsSUFBSSxDQUFDLDBDQUFpRCxPQUFQbEMsUUFBTyxNQUFJMUM7WUFDbEUsT0FBTztRQUNUO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFtRyxZQUFZcEUsS0FBYSxFQUduQztZQUhxQ2pCLE9BQUFBLGlFQUFlLEdBQUdDLFFBQUFBLGlFQUFnQjtRQUl4RSxNQUFNRSxTQUFTLENBQUNILE9BQU8sS0FBS0M7UUFDNUIsTUFBTXFCLGFBQWEsSUFBVSxPQUFOTCxPQUFNO1FBRTdCLE1BQU0sQ0FBQ2YsWUFBWUUsWUFBWSxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztZQUNsRG5ELG1EQUFRQSxDQUNMbUIsSUFBSSxDQUFDbEIsaURBQU1BLENBQUNtQixZQUFZLEVBQ3hCQyxNQUFNLENBQUMsa0NBRWMsT0FBYnBCLGlEQUFNQSxDQUFDeUgsS0FBSyxFQUFDLDJFQUVyQnBHLEVBQUUsQ0FBQyxVQUFVLFlBQ2I4QyxFQUFFLENBQUMsY0FBOENELE9BQWhDQSxZQUFXLHVCQUFnQyxPQUFYQSxhQUNqRGYsS0FBSyxDQUFDLGVBQWU7Z0JBQUVDLFdBQVc7WUFBTSxHQUN4Q0QsS0FBSyxDQUFDLFVBQVU7Z0JBQUVDLFdBQVc7WUFBTSxHQUNuQ0MsS0FBSyxDQUFDTixRQUFRQSxTQUFTRixRQUFRO1lBQ2xDOUMsbURBQVFBLENBQ0xtQixJQUFJLENBQUNsQixpREFBTUEsQ0FBQ21CLFlBQVksRUFDeEJDLE1BQU0sQ0FBQyxLQUFLO2dCQUFFa0MsT0FBTztnQkFBU0MsTUFBTTtZQUFLLEdBQ3pDbEMsRUFBRSxDQUFDLFVBQVUsWUFDYjhDLEVBQUUsQ0FBQyxjQUE4Q0QsT0FBaENBLFlBQVcsdUJBQWdDLE9BQVhBO1NBQ3JEO1FBRUQsSUFBSXBCLFdBQVdoQixLQUFLLEVBQUU7WUFDcEIsTUFBTSxJQUFJekIsTUFBTSwyQkFBb0QsT0FBekJ5QyxXQUFXaEIsS0FBSyxDQUFDUSxPQUFPO1FBQ3JFO1FBRUEsT0FBTztZQUNMbUIsT0FBT1gsV0FBVzlCLElBQUksSUFBSSxFQUFFO1lBQzVCMEMsT0FBT1YsWUFBWU0sS0FBSyxJQUFJO1FBQzlCO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGliL3NlcnZpY2VzL3ZlbmRvclNob3BzLnRzP2I0NjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3VwYWJhc2UsIFRBQkxFUyB9IGZyb20gJ0AvbGliL3N1cGFiYXNlJ1xuaW1wb3J0IHsgVmVuZG9yU2hvcCwgU2hvcFByb2R1Y3QsIFNob3BSZXZpZXcsIFNob3BGb2xsb3dlciB9IGZyb20gJ0AvdHlwZXMnXG5cbi8qKlxuICogVmVuZG9yU2hvcFNlcnZpY2UgLSBDb21wbGV0ZWx5IHJlYnVpbHQgd2l0aCBwcm9wZXIgZXJyb3IgaGFuZGxpbmcgYW5kIHJlbGF0aW9uc2hpcHNcbiAqXG4gKiBLZXkgaW1wcm92ZW1lbnRzOlxuICogLSBVc2VzIGNvcnJlY3QgZm9yZWlnbiBrZXkgY29uc3RyYWludCBuYW1lcyBmcm9tIG1pZ3JhdGlvbiBmaWxlc1xuICogLSBDb21wcmVoZW5zaXZlIGVycm9yIGhhbmRsaW5nIGFuZCBsb2dnaW5nXG4gKiAtIFByb3BlciB2YWxpZGF0aW9uIGZvciBhbGwgb3BlcmF0aW9uc1xuICogLSBPcHRpbWl6ZWQgcXVlcmllcyB3aXRoIHByb3BlciByZWxhdGlvbnNoaXBzXG4gKi9cbmV4cG9ydCBjbGFzcyBWZW5kb3JTaG9wU2VydmljZSB7XG4gIC8qKlxuICAgKiBDcmVhdGUgYSBuZXcgdmVuZG9yIHNob3Agd2l0aCBjb21wcmVoZW5zaXZlIHZhbGlkYXRpb25cbiAgICovXG4gIHN0YXRpYyBhc3luYyBjcmVhdGVTaG9wKHNob3BEYXRhOiBPbWl0PFZlbmRvclNob3AsICdpZCcgfCAnY3JlYXRlZF9hdCcgfCAndXBkYXRlZF9hdCcgfCAncmF0aW5nJyB8ICd0b3RhbF9yZXZpZXdzJyB8ICd0b3RhbF9wcm9kdWN0cycgfCAndG90YWxfc2FsZXMnPik6IFByb21pc2U8VmVuZG9yU2hvcD4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIXNob3BEYXRhLnVzZXJfaWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIElEIGlzIHJlcXVpcmVkJylcbiAgICAgIH1cblxuICAgICAgaWYgKCFzaG9wRGF0YS5uYW1lPy50cmltKCkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTaG9wIG5hbWUgaXMgcmVxdWlyZWQnKVxuICAgICAgfVxuXG4gICAgICBpZiAoIXNob3BEYXRhLmRlc2NyaXB0aW9uPy50cmltKCkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTaG9wIGRlc2NyaXB0aW9uIGlzIHJlcXVpcmVkJylcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBhbHJlYWR5IGhhcyBhIHNob3BcbiAgICAgIGNvbnN0IGV4aXN0aW5nVXNlclNob3AgPSBhd2FpdCB0aGlzLmdldFVzZXJTaG9wKHNob3BEYXRhLnVzZXJfaWQpXG4gICAgICBpZiAoZXhpc3RpbmdVc2VyU2hvcCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1lvdSBjYW4gb25seSBjcmVhdGUgb25lIHNob3AgcGVyIGFjY291bnQuIFlvdSBhbHJlYWR5IGhhdmUgYSBzaG9wLicpXG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKGBDcmVhdGluZyBzaG9wIGZvciB1c2VyOiAke3Nob3BEYXRhLnVzZXJfaWR9LCBuYW1lOiAke3Nob3BEYXRhLm5hbWV9YClcblxuICAgICAgLy8gR2VuZXJhdGUgc2x1ZyBmcm9tIG5hbWVcbiAgICAgIGNvbnN0IHNsdWcgPSBzaG9wRGF0YS5uYW1lLnRvTG93ZXJDYXNlKClcbiAgICAgICAgLnJlcGxhY2UoL1teYS16MC05XSsvZywgJy0nKVxuICAgICAgICAucmVwbGFjZSgvKF4tfC0kKS9nLCAnJylcblxuICAgICAgLy8gQ2hlY2sgaWYgc2x1ZyBhbHJlYWR5IGV4aXN0c1xuICAgICAgY29uc3QgeyBkYXRhOiBleGlzdGluZ1NsdWdTaG9wIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuVkVORE9SX1NIT1BTKVxuICAgICAgICAuc2VsZWN0KCdpZCcpXG4gICAgICAgIC5lcSgnc2x1ZycsIGAke3NsdWd9LSR7RGF0ZS5ub3coKX1gKVxuICAgICAgICAuc2luZ2xlKClcblxuICAgICAgY29uc3QgZmluYWxTbHVnID0gZXhpc3RpbmdTbHVnU2hvcCA/IGAke3NsdWd9LSR7RGF0ZS5ub3coKX0tJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgNSl9YCA6IGAke3NsdWd9LSR7RGF0ZS5ub3coKX1gXG5cbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5WRU5ET1JfU0hPUFMpXG4gICAgICAgIC5pbnNlcnQoe1xuICAgICAgICAgIC4uLnNob3BEYXRhLFxuICAgICAgICAgIG5hbWU6IHNob3BEYXRhLm5hbWUudHJpbSgpLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBzaG9wRGF0YS5kZXNjcmlwdGlvbi50cmltKCksXG4gICAgICAgICAgc2x1ZzogZmluYWxTbHVnLFxuICAgICAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgICAgIGlzX2ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgICByYXRpbmc6IDAsXG4gICAgICAgICAgdG90YWxfcmV2aWV3czogMCxcbiAgICAgICAgICB0b3RhbF9wcm9kdWN0czogMCxcbiAgICAgICAgICB0b3RhbF9zYWxlczogMFxuICAgICAgICB9KVxuICAgICAgICAuc2VsZWN0KGBcbiAgICAgICAgICAqLFxuICAgICAgICAgIHVzZXI6dXNlcnMhdmVuZG9yX3Nob3BzX3VzZXJfaWRfZmtleShpZCwgZnVsbF9uYW1lLCBlbWFpbCwgYXZhdGFyX3VybClcbiAgICAgICAgYClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBzaG9wOicsIGVycm9yKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgc2hvcDogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIGlmICghZGF0YSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1Nob3Agd2FzIG5vdCBjcmVhdGVkJylcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ1Nob3AgY3JlYXRlZCBzdWNjZXNzZnVsbHk6JywgZGF0YS5pZClcbiAgICAgIHJldHVybiBkYXRhXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1ZlbmRvclNob3BTZXJ2aWNlLmNyZWF0ZVNob3AgZXJyb3I6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgc2hvcCBieSBzbHVnIHdpdGggY29tcHJlaGVuc2l2ZSBlcnJvciBoYW5kbGluZ1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFNob3BCeVNsdWcoc2x1Zzogc3RyaW5nKTogUHJvbWlzZTxWZW5kb3JTaG9wIHwgbnVsbD4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIXNsdWc/LnRyaW0oKSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1Nob3Agc2x1ZyBpcyByZXF1aXJlZCcpXG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKGBGZXRjaGluZyBzaG9wIGJ5IHNsdWc6ICR7c2x1Z31gKVxuXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuVkVORE9SX1NIT1BTKVxuICAgICAgICAuc2VsZWN0KGBcbiAgICAgICAgICAqLFxuICAgICAgICAgIHVzZXI6dXNlcnMhdmVuZG9yX3Nob3BzX3VzZXJfaWRfZmtleShpZCwgZnVsbF9uYW1lLCBlbWFpbCwgYXZhdGFyX3VybClcbiAgICAgICAgYClcbiAgICAgICAgLmVxKCdzbHVnJywgc2x1Zy50cmltKCkpXG4gICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09ICdQR1JTVDExNicpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgU2hvcCBub3QgZm91bmQgd2l0aCBzbHVnOiAke3NsdWd9YClcbiAgICAgICAgICByZXR1cm4gbnVsbCAvLyBOb3QgZm91bmRcbiAgICAgICAgfVxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzaG9wIGJ5IHNsdWc6JywgZXJyb3IpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHNob3A6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICBpZiAoZGF0YSkge1xuICAgICAgICBjb25zb2xlLmxvZyhgRm91bmQgc2hvcDogJHtkYXRhLmlkfSB3aXRoIHNsdWc6ICR7c2x1Z31gKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gZGF0YVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdWZW5kb3JTaG9wU2VydmljZS5nZXRTaG9wQnlTbHVnIGVycm9yOicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IHVzZXIncyBzaG9wIHdpdGggY29tcHJlaGVuc2l2ZSBlcnJvciBoYW5kbGluZ1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFVzZXJTaG9wKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxWZW5kb3JTaG9wIHwgbnVsbD4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIXVzZXJJZCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgSUQgaXMgcmVxdWlyZWQnKVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhgRmV0Y2hpbmcgc2hvcCBmb3IgdXNlcjogJHt1c2VySWR9YClcblxuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlZFTkRPUl9TSE9QUylcbiAgICAgICAgLnNlbGVjdChgXG4gICAgICAgICAgKixcbiAgICAgICAgICB1c2VyOnVzZXJzIXZlbmRvcl9zaG9wc191c2VyX2lkX2ZrZXkoaWQsIGZ1bGxfbmFtZSwgZW1haWwsIGF2YXRhcl91cmwpXG4gICAgICAgIGApXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlICE9PSAnUEdSU1QxMTYnKSB7IC8vIFBHUlNUMTE2IGlzIFwibm90IGZvdW5kXCIgZXJyb3JcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdXNlciBzaG9wOicsIGVycm9yKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCB1c2VyIHNob3A6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICBpZiAoZGF0YSkge1xuICAgICAgICBjb25zb2xlLmxvZyhgRm91bmQgc2hvcCBmb3IgdXNlciAke3VzZXJJZH06ICR7ZGF0YS5pZH1gKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coYE5vIHNob3AgZm91bmQgZm9yIHVzZXI6ICR7dXNlcklkfWApXG4gICAgICB9XG5cbiAgICAgIHJldHVybiBkYXRhIHx8IG51bGxcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignVmVuZG9yU2hvcFNlcnZpY2UuZ2V0VXNlclNob3AgZXJyb3I6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYWxsIGFwcHJvdmVkIHNob3BzIHdpdGggcGFnaW5hdGlvbiBhbmQgY29tcHJlaGVuc2l2ZSBlcnJvciBoYW5kbGluZ1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldEFwcHJvdmVkU2hvcHMocGFnZTogbnVtYmVyID0gMSwgbGltaXQ6IG51bWJlciA9IDIwKTogUHJvbWlzZTx7XG4gICAgc2hvcHM6IFZlbmRvclNob3BbXVxuICAgIHRvdGFsOiBudW1iZXJcbiAgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBvZmZzZXQgPSAocGFnZSAtIDEpICogbGltaXRcblxuICAgICAgY29uc29sZS5sb2coYEZldGNoaW5nIGFwcHJvdmVkIHNob3BzIC0gcGFnZTogJHtwYWdlfSwgbGltaXQ6ICR7bGltaXR9YClcblxuICAgICAgY29uc3QgW2RhdGFSZXN1bHQsIGNvdW50UmVzdWx0XSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbShUQUJMRVMuVkVORE9SX1NIT1BTKVxuICAgICAgICAgIC5zZWxlY3QoYFxuICAgICAgICAgICAgKixcbiAgICAgICAgICAgIHVzZXI6dXNlcnMhdmVuZG9yX3Nob3BzX3VzZXJfaWRfZmtleShpZCwgZnVsbF9uYW1lLCBlbWFpbCwgYXZhdGFyX3VybClcbiAgICAgICAgICBgKVxuICAgICAgICAgIC5lcSgnc3RhdHVzJywgJ2FwcHJvdmVkJylcbiAgICAgICAgICAub3JkZXIoJ2lzX2ZlYXR1cmVkJywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgICAgICAgLnJhbmdlKG9mZnNldCwgb2Zmc2V0ICsgbGltaXQgLSAxKSxcbiAgICAgICAgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbShUQUJMRVMuVkVORE9SX1NIT1BTKVxuICAgICAgICAgIC5zZWxlY3QoJyonLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pXG4gICAgICAgICAgLmVxKCdzdGF0dXMnLCAnYXBwcm92ZWQnKVxuICAgICAgXSlcblxuICAgICAgaWYgKGRhdGFSZXN1bHQuZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgYXBwcm92ZWQgc2hvcHM6JywgZGF0YVJlc3VsdC5lcnJvcilcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggc2hvcHM6ICR7ZGF0YVJlc3VsdC5lcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIGlmIChjb3VudFJlc3VsdC5lcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBhcHByb3ZlZCBzaG9wcyBjb3VudDonLCBjb3VudFJlc3VsdC5lcnJvcilcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggc2hvcHMgY291bnQ6ICR7Y291bnRSZXN1bHQuZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhgU3VjY2Vzc2Z1bGx5IGZldGNoZWQgJHtkYXRhUmVzdWx0LmRhdGE/Lmxlbmd0aCB8fCAwfSBhcHByb3ZlZCBzaG9wcyAodG90YWw6ICR7Y291bnRSZXN1bHQuY291bnQgfHwgMH0pYClcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc2hvcHM6IGRhdGFSZXN1bHQuZGF0YSB8fCBbXSxcbiAgICAgICAgdG90YWw6IGNvdW50UmVzdWx0LmNvdW50IHx8IDBcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignVmVuZG9yU2hvcFNlcnZpY2UuZ2V0QXBwcm92ZWRTaG9wcyBlcnJvcjonLCBlcnJvcilcbiAgICAgIHRocm93IGVycm9yXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBzaG9wcyB3aXRoIGZpbHRlcnMgYW5kIGNvbXByZWhlbnNpdmUgZXJyb3IgaGFuZGxpbmdcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRTaG9wcyhcbiAgICBmaWx0ZXJzOiB7XG4gICAgICBzdGF0dXM/OiAncGVuZGluZycgfCAnYXBwcm92ZWQnIHwgJ3JlamVjdGVkJyB8ICdzdXNwZW5kZWQnXG4gICAgICBmZWF0dXJlZD86IGJvb2xlYW5cbiAgICAgIGNhdGVnb3J5Pzogc3RyaW5nXG4gICAgICBsb2NhdGlvbj86IHN0cmluZ1xuICAgICAgc2VhcmNoPzogc3RyaW5nXG4gICAgfSA9IHt9LFxuICAgIHBhZ2U6IG51bWJlciA9IDEsXG4gICAgbGltaXQ6IG51bWJlciA9IDIwXG4gICk6IFByb21pc2U8e1xuICAgIHNob3BzOiBWZW5kb3JTaG9wW11cbiAgICB0b3RhbDogbnVtYmVyXG4gIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgb2Zmc2V0ID0gKHBhZ2UgLSAxKSAqIGxpbWl0XG5cbiAgICAgIGNvbnNvbGUubG9nKGBGZXRjaGluZyBzaG9wcyB3aXRoIGZpbHRlcnM6YCwgZmlsdGVycywgYHBhZ2U6ICR7cGFnZX0sIGxpbWl0OiAke2xpbWl0fWApXG5cbiAgICAgIGxldCBxdWVyeSA9IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5WRU5ET1JfU0hPUFMpXG4gICAgICAgIC5zZWxlY3QoYFxuICAgICAgICAgICosXG4gICAgICAgICAgdXNlcjp1c2VycyF2ZW5kb3Jfc2hvcHNfdXNlcl9pZF9ma2V5KGlkLCBmdWxsX25hbWUsIGVtYWlsLCBhdmF0YXJfdXJsKVxuICAgICAgICBgKVxuICAgICAgICAub3JkZXIoJ2lzX2ZlYXR1cmVkJywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuXG4gICAgICBsZXQgY291bnRRdWVyeSA9IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5WRU5ET1JfU0hPUFMpXG4gICAgICAgIC5zZWxlY3QoJyonLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pXG5cbiAgICAgIC8vIEFwcGx5IGZpbHRlcnNcbiAgICAgIGlmIChmaWx0ZXJzLnN0YXR1cykge1xuICAgICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdzdGF0dXMnLCBmaWx0ZXJzLnN0YXR1cylcbiAgICAgICAgY291bnRRdWVyeSA9IGNvdW50UXVlcnkuZXEoJ3N0YXR1cycsIGZpbHRlcnMuc3RhdHVzKVxuICAgICAgfVxuXG4gICAgICBpZiAoZmlsdGVycy5mZWF0dXJlZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ2lzX2ZlYXR1cmVkJywgZmlsdGVycy5mZWF0dXJlZClcbiAgICAgICAgY291bnRRdWVyeSA9IGNvdW50UXVlcnkuZXEoJ2lzX2ZlYXR1cmVkJywgZmlsdGVycy5mZWF0dXJlZClcbiAgICAgIH1cblxuICAgICAgaWYgKGZpbHRlcnMuc2VhcmNoPy50cmltKCkpIHtcbiAgICAgICAgY29uc3Qgc2VhcmNoVGVybSA9IGAlJHtmaWx0ZXJzLnNlYXJjaC50cmltKCl9JWBcbiAgICAgICAgcXVlcnkgPSBxdWVyeS5vcihgbmFtZS5pbGlrZS4ke3NlYXJjaFRlcm19LGRlc2NyaXB0aW9uLmlsaWtlLiR7c2VhcmNoVGVybX1gKVxuICAgICAgICBjb3VudFF1ZXJ5ID0gY291bnRRdWVyeS5vcihgbmFtZS5pbGlrZS4ke3NlYXJjaFRlcm19LGRlc2NyaXB0aW9uLmlsaWtlLiR7c2VhcmNoVGVybX1gKVxuICAgICAgfVxuXG4gICAgICBpZiAoZmlsdGVycy5sb2NhdGlvbj8udHJpbSgpKSB7XG4gICAgICAgIHF1ZXJ5ID0gcXVlcnkuaWxpa2UoJ2FkZHJlc3MnLCBgJSR7ZmlsdGVycy5sb2NhdGlvbi50cmltKCl9JWApXG4gICAgICAgIGNvdW50UXVlcnkgPSBjb3VudFF1ZXJ5LmlsaWtlKCdhZGRyZXNzJywgYCUke2ZpbHRlcnMubG9jYXRpb24udHJpbSgpfSVgKVxuICAgICAgfVxuXG4gICAgICBjb25zdCBbZGF0YVJlc3VsdCwgY291bnRSZXN1bHRdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBxdWVyeS5yYW5nZShvZmZzZXQsIG9mZnNldCArIGxpbWl0IC0gMSksXG4gICAgICAgIGNvdW50UXVlcnlcbiAgICAgIF0pXG5cbiAgICAgIGlmIChkYXRhUmVzdWx0LmVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHNob3BzOicsIGRhdGFSZXN1bHQuZXJyb3IpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHNob3BzOiAke2RhdGFSZXN1bHQuZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICBpZiAoY291bnRSZXN1bHQuZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc2hvcHMgY291bnQ6JywgY291bnRSZXN1bHQuZXJyb3IpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHNob3BzIGNvdW50OiAke2NvdW50UmVzdWx0LmVycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coYFN1Y2Nlc3NmdWxseSBmZXRjaGVkICR7ZGF0YVJlc3VsdC5kYXRhPy5sZW5ndGggfHwgMH0gc2hvcHMgKHRvdGFsOiAke2NvdW50UmVzdWx0LmNvdW50IHx8IDB9KWApXG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHNob3BzOiBkYXRhUmVzdWx0LmRhdGEgfHwgW10sXG4gICAgICAgIHRvdGFsOiBjb3VudFJlc3VsdC5jb3VudCB8fCAwXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1ZlbmRvclNob3BTZXJ2aWNlLmdldFNob3BzIGVycm9yOicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IHVzZXIncyBzaG9wcyB3aXRoIGNvbXByZWhlbnNpdmUgZXJyb3IgaGFuZGxpbmdcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRVc2VyU2hvcHModXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPFZlbmRvclNob3BbXT4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIXVzZXJJZCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgSUQgaXMgcmVxdWlyZWQnKVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhgRmV0Y2hpbmcgc2hvcHMgZm9yIHVzZXI6ICR7dXNlcklkfWApXG5cbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5WRU5ET1JfU0hPUFMpXG4gICAgICAgIC5zZWxlY3QoYFxuICAgICAgICAgICosXG4gICAgICAgICAgdXNlcjp1c2VycyF2ZW5kb3Jfc2hvcHNfdXNlcl9pZF9ma2V5KGlkLCBmdWxsX25hbWUsIGVtYWlsLCBhdmF0YXJfdXJsKVxuICAgICAgICBgKVxuICAgICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VySWQpXG4gICAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdXNlciBzaG9wczonLCBlcnJvcilcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggdXNlciBzaG9wczogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKGBTdWNjZXNzZnVsbHkgZmV0Y2hlZCAke2RhdGE/Lmxlbmd0aCB8fCAwfSBzaG9wcyBmb3IgdXNlcjogJHt1c2VySWR9YClcblxuICAgICAgcmV0dXJuIGRhdGEgfHwgW11cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignVmVuZG9yU2hvcFNlcnZpY2UuZ2V0VXNlclNob3BzIGVycm9yOicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogVXBkYXRlIHNob3BcbiAgICovXG4gIHN0YXRpYyBhc3luYyB1cGRhdGVTaG9wKHNob3BJZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPFZlbmRvclNob3A+KTogUHJvbWlzZTxWZW5kb3JTaG9wPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKFRBQkxFUy5WRU5ET1JfU0hPUFMpXG4gICAgICAudXBkYXRlKHVwZGF0ZXMpXG4gICAgICAuZXEoJ2lkJywgc2hvcElkKVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gdXBkYXRlIHNob3A6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIHJldHVybiBkYXRhXG4gIH1cblxuICAvKipcbiAgICogUmVmcmVzaCBhbGwgc2hvcCBzdGF0aXN0aWNzIGZvciBhbGwgc2hvcHNcbiAgICovXG4gIHN0YXRpYyBhc3luYyByZWZyZXNoQWxsU2hvcFN0YXRpc3RpY3MoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCdSZWZyZXNoaW5nIHN0YXRpc3RpY3MgZm9yIGFsbCBzaG9wcy4uLicpXG5cbiAgICAgIC8vIEdldCBhbGwgYXBwcm92ZWQgc2hvcHNcbiAgICAgIGNvbnN0IHsgZGF0YTogc2hvcHMsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuVkVORE9SX1NIT1BTKVxuICAgICAgICAuc2VsZWN0KCdpZCwgbmFtZScpXG4gICAgICAgIC5lcSgnc3RhdHVzJywgJ2FwcHJvdmVkJylcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHNob3BzOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgaWYgKCFzaG9wcyB8fCBzaG9wcy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ05vIGFwcHJvdmVkIHNob3BzIGZvdW5kJylcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIC8vIFJlZnJlc2ggc3RhdGlzdGljcyBmb3IgZWFjaCBzaG9wXG4gICAgICBmb3IgKGNvbnN0IHNob3Agb2Ygc2hvcHMpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBhd2FpdCB0aGlzLnJlZnJlc2hTaG9wU3RhdGlzdGljcyhzaG9wLmlkKVxuICAgICAgICAgIGNvbnNvbGUubG9nKGDinJMgVXBkYXRlZCBzdGF0aXN0aWNzIGZvciBzaG9wOiAke3Nob3AubmFtZX1gKVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOKclyBGYWlsZWQgdG8gdXBkYXRlIHN0YXRpc3RpY3MgZm9yIHNob3AgJHtzaG9wLm5hbWV9OmAsIGVycm9yKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdDb21wbGV0ZWQgcmVmcmVzaGluZyBzdGF0aXN0aWNzIGZvciBhbGwgc2hvcHMnKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZWZyZXNoaW5nIGFsbCBzaG9wIHN0YXRpc3RpY3M6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBNYW51YWxseSByZWZyZXNoIHNob3Agc3RhdGlzdGljcyAocHJvZHVjdHMsIHJldmlld3MsIHJhdGluZywgc2FsZXMpXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgcmVmcmVzaFNob3BTdGF0aXN0aWNzKHNob3BJZDogc3RyaW5nKTogUHJvbWlzZTxWZW5kb3JTaG9wPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKGBSZWZyZXNoaW5nIHN0YXRpc3RpY3MgZm9yIHNob3A6ICR7c2hvcElkfWApXG5cbiAgICAgIC8vIEZpcnN0IGdldCB0aGUgcHJvZHVjdCBJRHMgZm9yIHRoaXMgc2hvcFxuICAgICAgY29uc3QgeyBkYXRhOiBzaG9wUHJvZHVjdHMsIGVycm9yOiBwcm9kdWN0c0Vycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuU0hPUF9QUk9EVUNUUylcbiAgICAgICAgLnNlbGVjdCgnaWQnKVxuICAgICAgICAuZXEoJ3Nob3BfaWQnLCBzaG9wSWQpXG5cbiAgICAgIGlmIChwcm9kdWN0c0Vycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHNob3AgcHJvZHVjdHM6JywgcHJvZHVjdHNFcnJvcilcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggc2hvcCBwcm9kdWN0czogJHtwcm9kdWN0c0Vycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgY29uc3QgcHJvZHVjdElkcyA9IHNob3BQcm9kdWN0cz8ubWFwKHAgPT4gcC5pZCkgfHwgW11cblxuICAgICAgLy8gR2V0IGN1cnJlbnQgc3RhdGlzdGljcyBmcm9tIHJlbGF0ZWQgdGFibGVzIHdpdGggaW5kaXZpZHVhbCBlcnJvciBoYW5kbGluZ1xuICAgICAgY29uc3QgW3Byb2R1Y3RzUmVzdWx0LCByZXZpZXdzUmVzdWx0LCBvcmRlcnNSZXN1bHQsIHZpZXdzUmVzdWx0XSA9IGF3YWl0IFByb21pc2UuYWxsU2V0dGxlZChbXG4gICAgICAgIC8vIENvdW50IHByb2R1Y3RzXG4gICAgICAgIHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oVEFCTEVTLlNIT1BfUFJPRFVDVFMpXG4gICAgICAgICAgLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSlcbiAgICAgICAgICAuZXEoJ3Nob3BfaWQnLCBzaG9wSWQpLFxuXG4gICAgICAgIC8vIEdldCBwcm9kdWN0IHJldmlld3MgZm9yIHJhdGluZyBjYWxjdWxhdGlvblxuICAgICAgICBwcm9kdWN0SWRzLmxlbmd0aCA+IDBcbiAgICAgICAgICA/IHN1cGFiYXNlXG4gICAgICAgICAgICAgIC5mcm9tKFRBQkxFUy5QUk9EVUNUX1JFVklFV1MpXG4gICAgICAgICAgICAgIC5zZWxlY3QoJ3JhdGluZycpXG4gICAgICAgICAgICAgIC5pbigncHJvZHVjdF9pZCcsIHByb2R1Y3RJZHMpXG4gICAgICAgICAgOiBQcm9taXNlLnJlc29sdmUoeyBkYXRhOiBbXSwgZXJyb3I6IG51bGwgfSksXG5cbiAgICAgICAgLy8gQ291bnQgY29tcGxldGVkIHNhbGVzXG4gICAgICAgIHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oVEFCTEVTLlNIT1BfT1JERVJTKVxuICAgICAgICAgIC5zZWxlY3QoJyonLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pXG4gICAgICAgICAgLmVxKCdzaG9wX2lkJywgc2hvcElkKVxuICAgICAgICAgIC5pbignc3RhdHVzJywgWydkZWxpdmVyZWQnLCAnY29tcGxldGVkJ10pLFxuXG4gICAgICAgIC8vIENhbGN1bGF0ZSB0b3RhbCB2aWV3cyBmcm9tIGFsbCBwcm9kdWN0c1xuICAgICAgICBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKFRBQkxFUy5TSE9QX1BST0RVQ1RTKVxuICAgICAgICAgIC5zZWxlY3QoJ3ZpZXdzJylcbiAgICAgICAgICAuZXEoJ3Nob3BfaWQnLCBzaG9wSWQpXG4gICAgICBdKVxuXG4gICAgICAvLyBQcm9jZXNzIHJlc3VsdHMgd2l0aCBlcnJvciBoYW5kbGluZ1xuICAgICAgY29uc3QgdG90YWxQcm9kdWN0cyA9IHByb2R1Y3RzUmVzdWx0LnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgJiYgIXByb2R1Y3RzUmVzdWx0LnZhbHVlLmVycm9yXG4gICAgICAgID8gcHJvZHVjdHNSZXN1bHQudmFsdWUuY291bnQgfHwgMFxuICAgICAgICA6IDBcblxuICAgICAgY29uc3QgcmV2aWV3cyA9IHJldmlld3NSZXN1bHQuc3RhdHVzID09PSAnZnVsZmlsbGVkJyAmJiAhcmV2aWV3c1Jlc3VsdC52YWx1ZS5lcnJvclxuICAgICAgICA/IHJldmlld3NSZXN1bHQudmFsdWUuZGF0YSB8fCBbXVxuICAgICAgICA6IFtdXG4gICAgICBjb25zdCB0b3RhbFJldmlld3MgPSByZXZpZXdzLmxlbmd0aFxuICAgICAgY29uc3QgYXZlcmFnZVJhdGluZyA9IHRvdGFsUmV2aWV3cyA+IDBcbiAgICAgICAgPyByZXZpZXdzLnJlZHVjZSgoc3VtLCByZXZpZXcpID0+IHN1bSArIHJldmlldy5yYXRpbmcsIDApIC8gdG90YWxSZXZpZXdzXG4gICAgICAgIDogMFxuXG4gICAgICBjb25zdCB0b3RhbFNhbGVzID0gb3JkZXJzUmVzdWx0LnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgJiYgIW9yZGVyc1Jlc3VsdC52YWx1ZS5lcnJvclxuICAgICAgICA/IG9yZGVyc1Jlc3VsdC52YWx1ZS5jb3VudCB8fCAwXG4gICAgICAgIDogMFxuXG4gICAgICBjb25zdCBwcm9kdWN0Vmlld3MgPSB2aWV3c1Jlc3VsdC5zdGF0dXMgPT09ICdmdWxmaWxsZWQnICYmICF2aWV3c1Jlc3VsdC52YWx1ZS5lcnJvclxuICAgICAgICA/IHZpZXdzUmVzdWx0LnZhbHVlLmRhdGEgfHwgW11cbiAgICAgICAgOiBbXVxuICAgICAgY29uc3QgdG90YWxWaWV3cyA9IHByb2R1Y3RWaWV3cy5yZWR1Y2UoKHN1bSwgcHJvZHVjdCkgPT4gc3VtICsgKHByb2R1Y3Qudmlld3MgfHwgMCksIDApXG5cbiAgICAgIC8vIExvZyBhbnkgZXJyb3JzIGZyb20gdGhlIHF1ZXJpZXNcbiAgICAgIGlmIChwcm9kdWN0c1Jlc3VsdC5zdGF0dXMgPT09ICdyZWplY3RlZCcpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gZ2V0IHByb2R1Y3RzIGNvdW50OicsIHByb2R1Y3RzUmVzdWx0LnJlYXNvbilcbiAgICAgIH1cbiAgICAgIGlmIChyZXZpZXdzUmVzdWx0LnN0YXR1cyA9PT0gJ3JlamVjdGVkJykge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBnZXQgcmV2aWV3czonLCByZXZpZXdzUmVzdWx0LnJlYXNvbilcbiAgICAgIH1cbiAgICAgIGlmIChvcmRlcnNSZXN1bHQuc3RhdHVzID09PSAncmVqZWN0ZWQnKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIGdldCBvcmRlcnMgY291bnQ6Jywgb3JkZXJzUmVzdWx0LnJlYXNvbilcbiAgICAgIH1cbiAgICAgIGlmICh2aWV3c1Jlc3VsdC5zdGF0dXMgPT09ICdyZWplY3RlZCcpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gZ2V0IHByb2R1Y3Qgdmlld3M6Jywgdmlld3NSZXN1bHQucmVhc29uKVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhgU2hvcCAke3Nob3BJZH0gc3RhdGlzdGljczpgLCB7XG4gICAgICAgIHRvdGFsUHJvZHVjdHMsXG4gICAgICAgIHRvdGFsUmV2aWV3cyxcbiAgICAgICAgYXZlcmFnZVJhdGluZyxcbiAgICAgICAgdG90YWxTYWxlcyxcbiAgICAgICAgdG90YWxWaWV3c1xuICAgICAgfSlcblxuICAgICAgLy8gVXBkYXRlIHRoZSBzaG9wIHdpdGggY2FsY3VsYXRlZCBzdGF0aXN0aWNzXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuVkVORE9SX1NIT1BTKVxuICAgICAgICAudXBkYXRlKHtcbiAgICAgICAgICB0b3RhbF9wcm9kdWN0czogdG90YWxQcm9kdWN0cyxcbiAgICAgICAgICB0b3RhbF9yZXZpZXdzOiB0b3RhbFJldmlld3MsXG4gICAgICAgICAgcmF0aW5nOiBOdW1iZXIoYXZlcmFnZVJhdGluZy50b0ZpeGVkKDIpKSxcbiAgICAgICAgICB0b3RhbF9zYWxlczogdG90YWxTYWxlcyxcbiAgICAgICAgICB0b3RhbF92aWV3czogdG90YWxWaWV3cyxcbiAgICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgfSlcbiAgICAgICAgLmVxKCdpZCcsIHNob3BJZClcbiAgICAgICAgLnNlbGVjdCgpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1cGRhdGUgc2hvcCBzdGF0aXN0aWNzOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgaWYgKCFkYXRhIHx8IGRhdGEubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgU2hvcCBub3QgZm91bmQ6ICR7c2hvcElkfWApXG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKGBTdWNjZXNzZnVsbHkgdXBkYXRlZCBzdGF0aXN0aWNzIGZvciBzaG9wOiAke3Nob3BJZH1gKVxuICAgICAgcmV0dXJuIGRhdGFbMF1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVmcmVzaGluZyBzaG9wIHN0YXRpc3RpY3M6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBBZGQgcHJvZHVjdCB0byBzaG9wXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgYWRkUHJvZHVjdFRvU2hvcChwcm9kdWN0RGF0YTogT21pdDxTaG9wUHJvZHVjdCwgJ2lkJyB8ICdjcmVhdGVkX2F0JyB8ICd1cGRhdGVkX2F0Jz4pOiBQcm9taXNlPFNob3BQcm9kdWN0PiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKFRBQkxFUy5TSE9QX1BST0RVQ1RTKVxuICAgICAgLmluc2VydChwcm9kdWN0RGF0YSlcbiAgICAgIC5zZWxlY3QoKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGFkZCBwcm9kdWN0IHRvIHNob3A6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIHJldHVybiBkYXRhXG4gIH1cblxuICAvKipcbiAgICogR2V0IHNob3AgcHJvZHVjdHMgLSBkZWxlZ2F0ZWQgdG8gU2hvcFByb2R1Y3RTZXJ2aWNlXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0U2hvcFByb2R1Y3RzKHNob3BJZDogc3RyaW5nLCBwYWdlOiBudW1iZXIgPSAxLCBsaW1pdDogbnVtYmVyID0gMjApOiBQcm9taXNlPHtcbiAgICBwcm9kdWN0czogU2hvcFByb2R1Y3RbXVxuICAgIHRvdGFsOiBudW1iZXJcbiAgfT4ge1xuICAgIC8vIEltcG9ydCBTaG9wUHJvZHVjdFNlcnZpY2UgZHluYW1pY2FsbHkgdG8gYXZvaWQgY2lyY3VsYXIgaW1wb3J0c1xuICAgIGNvbnN0IHsgU2hvcFByb2R1Y3RTZXJ2aWNlIH0gPSBhd2FpdCBpbXBvcnQoJy4vc2hvcFByb2R1Y3RzJylcbiAgICByZXR1cm4gU2hvcFByb2R1Y3RTZXJ2aWNlLmdldFNob3BQcm9kdWN0cyhzaG9wSWQsIHBhZ2UsIGxpbWl0KVxuICB9XG5cbiAgLyoqXG4gICAqIEFkZCByZXZpZXcgdG8gc2hvcFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGFkZFNob3BSZXZpZXcocmV2aWV3RGF0YTogT21pdDxTaG9wUmV2aWV3LCAnaWQnIHwgJ2NyZWF0ZWRfYXQnIHwgJ3VwZGF0ZWRfYXQnPik6IFByb21pc2U8U2hvcFJldmlldz4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbShUQUJMRVMuU0hPUF9SRVZJRVdTKVxuICAgICAgLmluc2VydChyZXZpZXdEYXRhKVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gYWRkIHJldmlldzogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGFcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgc2hvcCByZXZpZXdzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0U2hvcFJldmlld3Moc2hvcElkOiBzdHJpbmcsIHBhZ2U6IG51bWJlciA9IDEsIGxpbWl0OiBudW1iZXIgPSAxMCk6IFByb21pc2U8e1xuICAgIHJldmlld3M6IFNob3BSZXZpZXdbXVxuICAgIHRvdGFsOiBudW1iZXJcbiAgfT4ge1xuICAgIGNvbnN0IG9mZnNldCA9IChwYWdlIC0gMSkgKiBsaW1pdFxuXG4gICAgY29uc3QgW2RhdGFSZXN1bHQsIGNvdW50UmVzdWx0XSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgIHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5TSE9QX1JFVklFV1MpXG4gICAgICAgIC5zZWxlY3QoYFxuICAgICAgICAgICosXG4gICAgICAgICAgdXNlcjoke1RBQkxFUy5VU0VSU30hc2hvcF9yZXZpZXdzX3VzZXJfaWRfZmtleShpZCwgZnVsbF9uYW1lLCBhdmF0YXJfdXJsKVxuICAgICAgICBgKVxuICAgICAgICAuZXEoJ3Nob3BfaWQnLCBzaG9wSWQpXG4gICAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAucmFuZ2Uob2Zmc2V0LCBvZmZzZXQgKyBsaW1pdCAtIDEpLFxuICAgICAgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlNIT1BfUkVWSUVXUylcbiAgICAgICAgLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSlcbiAgICAgICAgLmVxKCdzaG9wX2lkJywgc2hvcElkKVxuICAgIF0pXG5cbiAgICBpZiAoZGF0YVJlc3VsdC5lcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggcmV2aWV3czogJHtkYXRhUmVzdWx0LmVycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgcmV2aWV3czogZGF0YVJlc3VsdC5kYXRhIHx8IFtdLFxuICAgICAgdG90YWw6IGNvdW50UmVzdWx0LmNvdW50IHx8IDBcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRm9sbG93L3VuZm9sbG93IHNob3BcbiAgICovXG4gIHN0YXRpYyBhc3luYyB0b2dnbGVTaG9wRm9sbG93KHNob3BJZDogc3RyaW5nLCB1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIC8vIENoZWNrIGlmIGFscmVhZHkgZm9sbG93aW5nXG4gICAgY29uc3QgeyBkYXRhOiBleGlzdGluZyB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKFRBQkxFUy5TSE9QX0ZPTExPV0VSUylcbiAgICAgIC5zZWxlY3QoJ2lkJylcbiAgICAgIC5lcSgnc2hvcF9pZCcsIHNob3BJZClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGV4aXN0aW5nKSB7XG4gICAgICAvLyBVbmZvbGxvd1xuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlNIT1BfRk9MTE9XRVJTKVxuICAgICAgICAuZGVsZXRlKClcbiAgICAgICAgLmVxKCdzaG9wX2lkJywgc2hvcElkKVxuICAgICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VySWQpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1bmZvbGxvdyBzaG9wOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBGb2xsb3dcbiAgICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5TSE9QX0ZPTExPV0VSUylcbiAgICAgICAgLmluc2VydCh7IHNob3BfaWQ6IHNob3BJZCwgdXNlcl9pZDogdXNlcklkIH0pXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmb2xsb3cgc2hvcDogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDaGVjayBpZiB1c2VyIGlzIGZvbGxvd2luZyBzaG9wXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgaXNGb2xsb3dpbmdTaG9wKHNob3BJZDogc3RyaW5nLCB1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbShUQUJMRVMuU0hPUF9GT0xMT1dFUlMpXG4gICAgICAuc2VsZWN0KCdpZCcpXG4gICAgICAuZXEoJ3Nob3BfaWQnLCBzaG9wSWQpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VySWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIHJldHVybiAhZXJyb3IgJiYgISFkYXRhXG4gIH1cblxuICAvKipcbiAgICogR2V0IHNob3AgZm9sbG93ZXJzIGNvdW50XG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0U2hvcEZvbGxvd2Vyc0NvdW50KHNob3BJZDogc3RyaW5nKTogUHJvbWlzZTxudW1iZXI+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBjb3VudCwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5TSE9QX0ZPTExPV0VSUylcbiAgICAgICAgLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSlcbiAgICAgICAgLmVxKCdzaG9wX2lkJywgc2hvcElkKVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKGBGYWlsZWQgdG8gZ2V0IGZvbGxvd2VycyBjb3VudCBmb3Igc2hvcCAke3Nob3BJZH06YCwgZXJyb3IubWVzc2FnZSlcbiAgICAgICAgcmV0dXJuIDBcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGNvdW50IHx8IDBcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKGBFcnJvciBnZXR0aW5nIGZvbGxvd2VycyBjb3VudCBmb3Igc2hvcCAke3Nob3BJZH06YCwgZXJyb3IpXG4gICAgICByZXR1cm4gMFxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTZWFyY2ggc2hvcHNcbiAgICovXG4gIHN0YXRpYyBhc3luYyBzZWFyY2hTaG9wcyhxdWVyeTogc3RyaW5nLCBwYWdlOiBudW1iZXIgPSAxLCBsaW1pdDogbnVtYmVyID0gMjApOiBQcm9taXNlPHtcbiAgICBzaG9wczogVmVuZG9yU2hvcFtdXG4gICAgdG90YWw6IG51bWJlclxuICB9PiB7XG4gICAgY29uc3Qgb2Zmc2V0ID0gKHBhZ2UgLSAxKSAqIGxpbWl0XG4gICAgY29uc3Qgc2VhcmNoVGVybSA9IGAlJHtxdWVyeX0lYFxuXG4gICAgY29uc3QgW2RhdGFSZXN1bHQsIGNvdW50UmVzdWx0XSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgIHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5WRU5ET1JfU0hPUFMpXG4gICAgICAgIC5zZWxlY3QoYFxuICAgICAgICAgICosXG4gICAgICAgICAgdXNlcjoke1RBQkxFUy5VU0VSU30hdmVuZG9yX3Nob3BzX3VzZXJfaWRfZmtleShpZCwgZnVsbF9uYW1lLCBlbWFpbCwgYXZhdGFyX3VybClcbiAgICAgICAgYClcbiAgICAgICAgLmVxKCdzdGF0dXMnLCAnYXBwcm92ZWQnKVxuICAgICAgICAub3IoYG5hbWUuaWxpa2UuJHtzZWFyY2hUZXJtfSxkZXNjcmlwdGlvbi5pbGlrZS4ke3NlYXJjaFRlcm19YClcbiAgICAgICAgLm9yZGVyKCdpc19mZWF0dXJlZCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAub3JkZXIoJ3JhdGluZycsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAucmFuZ2Uob2Zmc2V0LCBvZmZzZXQgKyBsaW1pdCAtIDEpLFxuICAgICAgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlZFTkRPUl9TSE9QUylcbiAgICAgICAgLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSlcbiAgICAgICAgLmVxKCdzdGF0dXMnLCAnYXBwcm92ZWQnKVxuICAgICAgICAub3IoYG5hbWUuaWxpa2UuJHtzZWFyY2hUZXJtfSxkZXNjcmlwdGlvbi5pbGlrZS4ke3NlYXJjaFRlcm19YClcbiAgICBdKVxuXG4gICAgaWYgKGRhdGFSZXN1bHQuZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHNlYXJjaCBzaG9wczogJHtkYXRhUmVzdWx0LmVycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgc2hvcHM6IGRhdGFSZXN1bHQuZGF0YSB8fCBbXSxcbiAgICAgIHRvdGFsOiBjb3VudFJlc3VsdC5jb3VudCB8fCAwXG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsic3VwYWJhc2UiLCJUQUJMRVMiLCJWZW5kb3JTaG9wU2VydmljZSIsImNyZWF0ZVNob3AiLCJzaG9wRGF0YSIsInVzZXJfaWQiLCJFcnJvciIsIm5hbWUiLCJ0cmltIiwiZGVzY3JpcHRpb24iLCJleGlzdGluZ1VzZXJTaG9wIiwiZ2V0VXNlclNob3AiLCJjb25zb2xlIiwibG9nIiwic2x1ZyIsInRvTG93ZXJDYXNlIiwicmVwbGFjZSIsImRhdGEiLCJleGlzdGluZ1NsdWdTaG9wIiwiZnJvbSIsIlZFTkRPUl9TSE9QUyIsInNlbGVjdCIsImVxIiwiRGF0ZSIsIm5vdyIsInNpbmdsZSIsImZpbmFsU2x1ZyIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsImVycm9yIiwiaW5zZXJ0Iiwic3RhdHVzIiwiaXNfZmVhdHVyZWQiLCJyYXRpbmciLCJ0b3RhbF9yZXZpZXdzIiwidG90YWxfcHJvZHVjdHMiLCJ0b3RhbF9zYWxlcyIsIm1lc3NhZ2UiLCJpZCIsImdldFNob3BCeVNsdWciLCJjb2RlIiwidXNlcklkIiwiZ2V0QXBwcm92ZWRTaG9wcyIsInBhZ2UiLCJsaW1pdCIsImRhdGFSZXN1bHQiLCJvZmZzZXQiLCJjb3VudFJlc3VsdCIsIlByb21pc2UiLCJhbGwiLCJvcmRlciIsImFzY2VuZGluZyIsInJhbmdlIiwiY291bnQiLCJoZWFkIiwibGVuZ3RoIiwic2hvcHMiLCJ0b3RhbCIsImdldFNob3BzIiwiZmlsdGVycyIsInF1ZXJ5IiwiY291bnRRdWVyeSIsImZlYXR1cmVkIiwidW5kZWZpbmVkIiwic2VhcmNoIiwic2VhcmNoVGVybSIsIm9yIiwibG9jYXRpb24iLCJpbGlrZSIsImdldFVzZXJTaG9wcyIsInVwZGF0ZVNob3AiLCJzaG9wSWQiLCJ1cGRhdGVzIiwidXBkYXRlIiwicmVmcmVzaEFsbFNob3BTdGF0aXN0aWNzIiwic2hvcCIsInJlZnJlc2hTaG9wU3RhdGlzdGljcyIsInNob3BQcm9kdWN0cyIsInByb2R1Y3RzRXJyb3IiLCJTSE9QX1BST0RVQ1RTIiwicHJvZHVjdElkcyIsIm1hcCIsInAiLCJwcm9kdWN0c1Jlc3VsdCIsInJldmlld3NSZXN1bHQiLCJvcmRlcnNSZXN1bHQiLCJ2aWV3c1Jlc3VsdCIsImFsbFNldHRsZWQiLCJQUk9EVUNUX1JFVklFV1MiLCJpbiIsInJlc29sdmUiLCJTSE9QX09SREVSUyIsInRvdGFsUHJvZHVjdHMiLCJ2YWx1ZSIsInJldmlld3MiLCJ0b3RhbFJldmlld3MiLCJhdmVyYWdlUmF0aW5nIiwicmVkdWNlIiwic3VtIiwicmV2aWV3IiwidG90YWxTYWxlcyIsInByb2R1Y3RWaWV3cyIsInRvdGFsVmlld3MiLCJwcm9kdWN0Iiwidmlld3MiLCJ3YXJuIiwicmVhc29uIiwiTnVtYmVyIiwidG9GaXhlZCIsInRvdGFsX3ZpZXdzIiwidXBkYXRlZF9hdCIsInRvSVNPU3RyaW5nIiwiYWRkUHJvZHVjdFRvU2hvcCIsInByb2R1Y3REYXRhIiwiZ2V0U2hvcFByb2R1Y3RzIiwiU2hvcFByb2R1Y3RTZXJ2aWNlIiwiYWRkU2hvcFJldmlldyIsInJldmlld0RhdGEiLCJTSE9QX1JFVklFV1MiLCJnZXRTaG9wUmV2aWV3cyIsIlVTRVJTIiwidG9nZ2xlU2hvcEZvbGxvdyIsImV4aXN0aW5nIiwiU0hPUF9GT0xMT1dFUlMiLCJkZWxldGUiLCJzaG9wX2lkIiwiaXNGb2xsb3dpbmdTaG9wIiwiZ2V0U2hvcEZvbGxvd2Vyc0NvdW50Iiwic2VhcmNoU2hvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/vendorShops.ts\n"));

/***/ })

});